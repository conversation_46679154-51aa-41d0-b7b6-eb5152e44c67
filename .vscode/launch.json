{"configurations": [{"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:m.o.a}/PRDGenerator-Fixed", "name": "Debug PRDGenerator (PRDGenerator-Fixed)", "program": "${workspaceFolder:m.o.a}/PRDGenerator-Fixed/.build/debug/PRDGenerator", "preLaunchTask": "swift: Build Debug PRDGenerator (PRDGenerator-Fixed)"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:m.o.a}/PRDGenerator-Fixed", "name": "Release PRDGenerator (PRDGenerator-Fixed)", "program": "${workspaceFolder:m.o.a}/PRDGenerator-Fixed/.build/release/PRDGenerator", "preLaunchTask": "swift: Build Release PRDGenerator (PRDGenerator-Fixed)"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:m.o.a}/PRDGenerator-Working", "name": "Debug PRDGenerator (PRDGenerator-Working)", "program": "${workspaceFolder:m.o.a}/PRDGenerator-Working/.build/debug/PRDGenerator", "preLaunchTask": "swift: Build Debug PRDGenerator (PRDGenerator-Working)"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:m.o.a}/PRDGenerator-Working", "name": "Release PRDGenerator (PRDGenerator-Working)", "program": "${workspaceFolder:m.o.a}/PRDGenerator-Working/.build/release/PRDGenerator", "preLaunchTask": "swift: Build Release PRDGenerator (PRDGenerator-Working)"}]}