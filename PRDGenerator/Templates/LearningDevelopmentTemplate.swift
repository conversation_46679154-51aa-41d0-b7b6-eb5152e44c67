import Foundation

struct LearningDevelopmentTemplate {
    static let sections: [PRDSection] = [
        // Project Overview
        PRDSection(
            title: "Project Overview",
            description: "High-level description of the learning and development initiative",
            sectionType: "overview",
            order: 1,
            questions: [
                PRDQuestion(
                    text: "What is the name of this L&D project?",
                    questionType: .textInput,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "e.g., Leadership Development Program",
                    helpText: "Provide a clear, descriptive name for your learning initiative"
                ),
                PRDQuestion(
                    text: "Provide a brief description of the learning objectives",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe what learners will achieve...",
                    helpText: "Include key skills, knowledge, or competencies to be developed"
                ),
                PRDQuestion(
                    text: "What is the target audience for this program?",
                    questionType: .multipleChoice,
                    options: ["New employees", "Mid-level managers", "Senior leadership", "Technical staff", "Sales team", "Customer service", "All employees", "External customers"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select all applicable target groups"
                ),
                PRDQuestion(
                    text: "What is the expected duration of the program?",
                    questionType: .singleSelect,
                    options: ["1-2 hours", "Half day", "Full day", "1 week", "2-4 weeks", "1-3 months", "3-6 months", "6+ months", "Ongoing"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Consider both active learning time and practice period"
                )
            ],
            isRequired: true
        ),
        
        // Learning Goals & Outcomes
        PRDSection(
            title: "Learning Goals & Outcomes",
            description: "Specific, measurable learning objectives and expected outcomes",
            sectionType: "goals",
            order: 2,
            questions: [
                PRDQuestion(
                    text: "List the primary learning objectives (what learners will know)",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "• Understand key concepts of...\n• Recognize the importance of...",
                    helpText: "Focus on knowledge acquisition and understanding"
                ),
                PRDQuestion(
                    text: "List the skill-based objectives (what learners will be able to do)",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "• Demonstrate ability to...\n• Apply techniques for...",
                    helpText: "Focus on practical skills and applications"
                ),
                PRDQuestion(
                    text: "How will learning success be measured?",
                    questionType: .multipleChoice,
                    options: ["Pre/post assessments", "Practical demonstrations", "Project completion", "Peer feedback", "Manager evaluation", "Self-assessment", "Certification exam", "Performance metrics"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select all applicable measurement methods"
                ),
                PRDQuestion(
                    text: "What are the expected business outcomes?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 4,
                    placeholder: "• Improved productivity by...\n• Reduced errors in...",
                    helpText: "Connect learning to business impact and ROI"
                )
            ],
            isRequired: true
        ),
        
        // Content Structure & Delivery
        PRDSection(
            title: "Content Structure & Delivery",
            description: "How the learning content will be organized and delivered",
            sectionType: "content",
            order: 3,
            questions: [
                PRDQuestion(
                    text: "What delivery methods will be used?",
                    questionType: .multipleChoice,
                    options: ["In-person workshops", "Virtual instructor-led", "Self-paced online", "Blended learning", "Microlearning", "Video-based", "Interactive simulations", "Mobile learning"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select all applicable delivery methods"
                ),
                PRDQuestion(
                    text: "Outline the main content modules or topics",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Module 1: Introduction to...\nModule 2: Advanced...",
                    helpText: "Provide a logical sequence of learning modules"
                ),
                PRDQuestion(
                    text: "What interactive elements will be included?",
                    questionType: .multipleChoice,
                    options: ["Quizzes and assessments", "Discussion forums", "Group activities", "Case studies", "Role-playing", "Simulations", "Hands-on exercises", "Peer collaboration"],
                    isRequired: false,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select engagement and interaction methods"
                ),
                PRDQuestion(
                    text: "Are there any prerequisite skills or knowledge required?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 4,
                    placeholder: "Learners should have...",
                    helpText: "List any required background knowledge or skills"
                )
            ],
            isRequired: true
        ),
        
        // Technology Requirements
        PRDSection(
            title: "Technology Requirements",
            description: "Technical specifications and platform requirements",
            sectionType: "technology",
            order: 4,
            questions: [
                PRDQuestion(
                    text: "What learning management system (LMS) will be used?",
                    questionType: .singleSelect,
                    options: ["Moodle", "Canvas", "Blackboard", "Cornerstone OnDemand", "Workday Learning", "SAP SuccessFactors", "Custom platform", "No LMS needed", "To be determined"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select the primary platform for content delivery"
                ),
                PRDQuestion(
                    text: "What devices/platforms need to be supported?",
                    questionType: .multipleChoice,
                    options: ["Desktop computers", "Laptops", "Tablets", "Smartphones", "iOS devices", "Android devices", "Web browsers", "Mobile apps"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Consider your learners' typical access methods"
                ),
                PRDQuestion(
                    text: "Are there any specific technical integrations needed?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 3,
                    placeholder: "Integration with HR systems, SSO, reporting tools...",
                    helpText: "List any required system integrations"
                ),
                PRDQuestion(
                    text: "What are the accessibility requirements?",
                    questionType: .multipleChoice,
                    options: ["WCAG 2.1 AA compliance", "Screen reader support", "Closed captions", "Multiple language support", "High contrast mode", "Keyboard navigation", "Mobile accessibility"],
                    isRequired: false,
                    order: 4,
                    placeholder: nil,
                    helpText: "Ensure inclusive design for all learners"
                )
            ],
            isRequired: true
        ),
        
        // Implementation Timeline
        PRDSection(
            title: "Implementation Timeline",
            description: "Project phases, milestones, and delivery schedule",
            sectionType: "timeline",
            order: 5,
            questions: [
                PRDQuestion(
                    text: "What is the target launch date?",
                    questionType: .dateInput,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "When do you need the program to be available to learners?"
                ),
                PRDQuestion(
                    text: "Outline the key project phases and milestones",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Phase 1: Content development (Week 1-4)\nPhase 2: Platform setup (Week 5-6)...",
                    helpText: "Break down the project into manageable phases"
                ),
                PRDQuestion(
                    text: "Are there any critical dependencies or constraints?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 3,
                    placeholder: "Dependent on SME availability, budget approval...",
                    helpText: "Identify potential blockers or dependencies"
                ),
                PRDQuestion(
                    text: "What is the rollout strategy?",
                    questionType: .singleSelect,
                    options: ["Pilot group first", "Department by department", "All learners at once", "Regional rollout", "Role-based rollout", "Voluntary enrollment", "Mandatory training"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "How will the program be introduced to learners?"
                )
            ],
            isRequired: true
        ),
        
        // Success Metrics & Evaluation
        PRDSection(
            title: "Success Metrics & Evaluation",
            description: "How the program's effectiveness will be measured and evaluated",
            sectionType: "metrics",
            order: 6,
            questions: [
                PRDQuestion(
                    text: "What are the key performance indicators (KPIs)?",
                    questionType: .multipleChoice,
                    options: ["Completion rates", "Assessment scores", "Time to competency", "Learner satisfaction", "Knowledge retention", "Behavior change", "Business impact", "ROI measurement"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select the most important metrics for success"
                ),
                PRDQuestion(
                    text: "How will learner feedback be collected?",
                    questionType: .multipleChoice,
                    options: ["Post-training surveys", "Focus groups", "One-on-one interviews", "Continuous feedback", "Peer reviews", "Manager feedback", "Self-reflection tools"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Plan for ongoing feedback collection"
                ),
                PRDQuestion(
                    text: "What are the target success metrics?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 3,
                    placeholder: "• 90% completion rate\n• Average satisfaction score of 4.5/5\n• 80% pass rate on assessments",
                    helpText: "Set specific, measurable targets"
                ),
                PRDQuestion(
                    text: "How often will the program be reviewed and updated?",
                    questionType: .singleSelect,
                    options: ["Monthly", "Quarterly", "Semi-annually", "Annually", "After each cohort", "Based on feedback", "As needed"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Plan for continuous improvement"
                )
            ],
            isRequired: true
        )
    ]
}