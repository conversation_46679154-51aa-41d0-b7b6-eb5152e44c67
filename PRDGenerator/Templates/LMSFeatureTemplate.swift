import Foundation

struct LMSFeatureTemplate {
    static let sections: [PRDSection] = [
        // Feature Overview
        PRDSection(
            title: "LMS Feature Overview",
            description: "High-level description of the Learning Management System feature",
            sectionType: "overview",
            order: 1,
            questions: [
                PRDQuestion(
                    text: "What is the name of this LMS feature?",
                    questionType: .textInput,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "e.g., Advanced Assessment Engine, Social Learning Hub",
                    helpText: "Provide a clear, descriptive name for the LMS feature"
                ),
                PRDQuestion(
                    text: "What problem does this feature solve for learners?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe the learner pain points this feature addresses",
                    helpText: "Focus on the learner experience and educational outcomes"
                ),
                PRDQuestion(
                    text: "What problem does this feature solve for instructors/administrators?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe how this feature helps educators and administrators",
                    helpText: "Consider workflow improvements, time savings, and management benefits"
                ),
                PRDQuestion(
                    text: "What type of LMS feature is this?",
                    questionType: .singleSelect,
                    options: ["Assessment & Testing", "Content Delivery", "Communication & Collaboration", "Analytics & Reporting", "User Management", "Gamification", "Mobile Learning", "Integration", "Accessibility", "Other"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select the primary category for this LMS feature"
                )
            ],
            isRequired: true
        ),
        
        // User Stories & Use Cases
        PRDSection(
            title: "User Stories & Use Cases",
            description: "Detailed user scenarios and use cases for the LMS feature",
            sectionType: "user_stories",
            order: 2,
            questions: [
                PRDQuestion(
                    text: "What are the primary user stories for learners?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "As a learner, I want to... so that I can...",
                    helpText: "Write user stories from the learner's perspective using the format: As a [user], I want [goal] so that [benefit]"
                ),
                PRDQuestion(
                    text: "What are the primary user stories for instructors?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "As an instructor, I want to... so that I can...",
                    helpText: "Write user stories from the instructor's perspective"
                ),
                PRDQuestion(
                    text: "What are the primary user stories for administrators?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "As an administrator, I want to... so that I can...",
                    helpText: "Write user stories from the administrator's perspective"
                ),
                PRDQuestion(
                    text: "What are the edge cases or exceptional scenarios?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 4,
                    placeholder: "Describe unusual situations, error conditions, or complex scenarios",
                    helpText: "Consider what happens when things go wrong or in unusual circumstances"
                )
            ],
            isRequired: true
        ),
        
        // Functional Requirements
        PRDSection(
            title: "Functional Requirements",
            description: "Detailed functional specifications for the LMS feature",
            sectionType: "functional",
            order: 3,
            questions: [
                PRDQuestion(
                    text: "What are the core functional requirements?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "List the essential functions this feature must perform",
                    helpText: "Be specific about what the feature should do, not how it should do it"
                ),
                PRDQuestion(
                    text: "What user interface elements are required?",
                    questionType: .multipleChoice,
                    options: ["Forms", "Tables/Lists", "Charts/Graphs", "Media Players", "File Upload", "Text Editor", "Calendar", "Search", "Filters", "Navigation Menus", "Dashboards", "Notifications"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select all UI components needed for this feature"
                ),
                PRDQuestion(
                    text: "What data needs to be captured, stored, or processed?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe the data requirements, including types, formats, and relationships",
                    helpText: "Consider user data, content data, analytics data, and system data"
                ),
                PRDQuestion(
                    text: "What business rules and validation logic are required?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe rules, constraints, and validation requirements",
                    helpText: "Include data validation, business logic, and system constraints"
                )
            ],
            isRequired: true
        ),
        
        // Technical Specifications
        PRDSection(
            title: "Technical Specifications",
            description: "Technical requirements and constraints for the LMS feature",
            sectionType: "technical",
            order: 4,
            questions: [
                PRDQuestion(
                    text: "What are the performance requirements?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "Specify response times, throughput, concurrent users, etc.",
                    helpText: "Include specific metrics like page load times, API response times, and capacity requirements"
                ),
                PRDQuestion(
                    text: "What integrations with existing LMS components are needed?",
                    questionType: .multipleChoice,
                    options: ["User Authentication", "Course Management", "Grade Book", "Content Library", "Messaging System", "Calendar", "Reporting Engine", "Payment Processing", "External APIs"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select existing LMS components this feature must integrate with"
                ),
                PRDQuestion(
                    text: "What external systems or APIs need to be integrated?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 3,
                    placeholder: "List external services, APIs, or systems to integrate with",
                    helpText: "Include third-party services, enterprise systems, or external data sources"
                ),
                PRDQuestion(
                    text: "What are the security and privacy requirements?",
                    questionType: .multipleChoice,
                    options: ["User Authentication", "Role-based Access Control", "Data Encryption", "FERPA Compliance", "GDPR Compliance", "Audit Logging", "Secure File Storage", "API Security"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select security and privacy requirements for this feature"
                )
            ],
            isRequired: true
        ),
        
        // User Experience Design
        PRDSection(
            title: "User Experience Design",
            description: "UX/UI requirements and design considerations",
            sectionType: "ux_design",
            order: 5,
            questions: [
                PRDQuestion(
                    text: "What are the key user experience goals?",
                    questionType: .multipleChoice,
                    options: ["Ease of Use", "Accessibility", "Mobile Responsiveness", "Fast Performance", "Intuitive Navigation", "Consistent Design", "Personalization", "Engagement"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select the primary UX objectives for this feature"
                ),
                PRDQuestion(
                    text: "What accessibility requirements must be met?",
                    questionType: .multipleChoice,
                    options: ["WCAG 2.1 AA Compliance", "Screen Reader Support", "Keyboard Navigation", "High Contrast Mode", "Text Scaling", "Alternative Text", "Closed Captions", "Voice Commands"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select accessibility standards and features to implement"
                ),
                PRDQuestion(
                    text: "What devices and screen sizes need to be supported?",
                    questionType: .multipleChoice,
                    options: ["Desktop (1920x1080+)", "Laptop (1366x768+)", "Tablet (768x1024)", "Mobile Phone (375x667+)", "Large Displays (2560x1440+)"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select all device types and screen sizes to support"
                ),
                PRDQuestion(
                    text: "Are there specific design patterns or UI frameworks to follow?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 4,
                    placeholder: "Describe design system, style guides, or UI frameworks to use",
                    helpText: "Include existing design systems, brand guidelines, or UI component libraries"
                )
            ],
            isRequired: true
        ),
        
        // Testing & Quality Assurance
        PRDSection(
            title: "Testing & Quality Assurance",
            description: "Testing requirements and quality criteria for the LMS feature",
            sectionType: "testing",
            order: 6,
            questions: [
                PRDQuestion(
                    text: "What types of testing are required?",
                    questionType: .multipleChoice,
                    options: ["Unit Testing", "Integration Testing", "User Acceptance Testing", "Performance Testing", "Security Testing", "Accessibility Testing", "Cross-browser Testing", "Mobile Testing"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select all testing types needed for this feature"
                ),
                PRDQuestion(
                    text: "What are the acceptance criteria for this feature?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Define specific, measurable criteria for feature acceptance",
                    helpText: "Use clear, testable statements that define when the feature is complete and working correctly"
                ),
                PRDQuestion(
                    text: "What are the key scenarios that must be tested?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "List critical user workflows and scenarios to test",
                    helpText: "Include happy path, error conditions, and edge cases"
                )
            ],
            isRequired: true
        )
    ]
}