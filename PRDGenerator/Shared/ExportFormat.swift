import Foundation

enum ExportFormat: String, CaseIterable {
    case pdf = "PDF"
    case html = "HTML"
    case rtf = "RTF"
    case word = "Word"
    case markdown = "Markdown"
    case plainText = "Plain Text"
    
    var fileExtension: String {
        switch self {
        case .pdf: return "pdf"
        case .html: return "html"
        case .rtf: return "rtf"
        case .word: return "doc"
        case .markdown: return "md"
        case .plainText: return "txt"
        }
    }
    
    var mimeType: String {
        switch self {
        case .pdf: return "application/pdf"
        case .html: return "text/html"
        case .rtf: return "application/rtf"
        case .word: return "application/msword"
        case .markdown: return "text/markdown"
        case .plainText: return "text/plain"
        }
    }
}