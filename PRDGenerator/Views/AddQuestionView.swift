import SwiftUI

struct AddQuestionView: View {
    @ObservedObject var section: Section
    let dataService: DataService
    @Environment(\.dismiss) private var dismiss
    
    @State private var questionText = ""
    @State private var selectedQuestionType: QuestionType = .textInput
    @State private var isRequired = false
    @State private var options: [String] = [""]
    @State private var placeholder = ""
    @State private var helpText = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Question Details") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Question Text")
                            .font(.headline)
                        
                        TextEditor(text: $questionText)
                            .frame(minHeight: 60)
                            .padding(4)
                            .background(Color(NSColor.textBackgroundColor))
                            .cornerRadius(4)
                            .overlay(
                                RoundedRectangle(cornerRadius: 4)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                            )
                    }
                    
                    Picker("Question Type", selection: $selectedQuestionType) {
                        ForEach(QuestionType.allCases, id: \.self) { type in
                            Text(type.rawValue.capitalized).tag(type)
                        }
                    }
                    .pickerStyle(.menu)
                    
                    Toggle("Required Question", isOn: $isRequired)
                }
                
                if selectedQuestionType == .multipleChoice || selectedQuestionType == .singleSelect {
                    Section("Answer Options") {
                        ForEach(options.indices, id: \.self) { index in
                            HStack {
                                TextField("Option \(index + 1)", text: $options[index])
                                
                                if options.count > 1 {
                                    Button(action: {
                                        options.remove(at: index)
                                    }) {
                                        Image(systemName: "minus.circle.fill")
                                            .foregroundColor(.red)
                                    }
                                    .buttonStyle(.plain)
                                }
                            }
                        }
                        
                        Button("Add Option") {
                            options.append("")
                        }
                        .buttonStyle(.borderless)
                    }
                }
                
                Section("Additional Settings") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Placeholder Text (Optional)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("Enter placeholder text...", text: $placeholder)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Help Text (Optional)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        TextField("Enter help text...", text: $helpText)
                            .textFieldStyle(.roundedBorder)
                    }
                }
            }
            .navigationTitle("Add Question")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Add") {
                        addQuestion()
                    }
                    .disabled(questionText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
        .frame(width: 500, height: 600)
    }
    
    private func addQuestion() {
        let trimmedText = questionText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else { return }
        
        let question = dataService.addQuestion(
            to: section,
            text: trimmedText,
            questionType: selectedQuestionType,
            isRequired: isRequired
        )
        
        // Set options for multiple choice/select questions
        if selectedQuestionType == .multipleChoice || selectedQuestionType == .singleSelect {
            let validOptions = options.compactMap { option in
                let trimmed = option.trimmingCharacters(in: .whitespacesAndNewlines)
                return trimmed.isEmpty ? nil : trimmed
            }
            if !validOptions.isEmpty {
                question.setOptions(validOptions)
            }
        }
        
        dataService.saveContext()
        dismiss()
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleSection = Section(context: context)
    sampleSection.title = "Sample Section"
    
    return AddQuestionView(section: sampleSection, dataService: DataService.shared)
}