import SwiftUI

struct WizardView: View {
    @ObservedObject var document: PRDDocument
    let dataService: DataService
    @Environment(\.dismiss) private var dismiss
    
    @StateObject private var viewModel: WizardViewModel
    @State private var showingCompletionAlert = false
    
    init(document: PRDDocument, dataService: DataService) {
        self.document = document
        self.dataService = dataService
        self._viewModel = StateObject(wrappedValue: WizardViewModel(document: document, dataService: dataService))
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with progress
            WizardHeaderView(viewModel: viewModel)
            
            Divider()
            
            // Main content
            if viewModel.isCompleted {
                WizardCompletionView(document: document) {
                    dismiss()
                }
            } else {
                HSplitView {
                    // Left sidebar - Section navigation
                    WizardSidebarView(viewModel: viewModel)
                        .frame(minWidth: 250, maxWidth: 300)
                    
                    // Main question area
                    WizardQuestionView(viewModel: viewModel)
                        .frame(minWidth: 500)
                }
            }
            
            if !viewModel.isCompleted {
                Divider()
                
                // Navigation controls
                WizardNavigationView(viewModel: viewModel) {
                    showingCompletionAlert = true
                }
            }
        }
        .navigationTitle("Document Wizard")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                Button("Exit Wizard") {
                    dismiss()
                }
            }
        }
        .alert("Complete Document", isPresented: $showingCompletionAlert) {
            Button("Continue Editing", role: .cancel) { }
            Button("Mark as Complete") {
                viewModel.completeWizard()
            }
        } message: {
            Text("Are you ready to mark this document as complete? You can always return to edit it later.")
        }
    }
}

struct WizardHeaderView: View {
    @ObservedObject var viewModel: WizardViewModel
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Question \(viewModel.currentQuestionNumber) of \(viewModel.totalQuestions)")
                        .font(.headline)
                        .fontWeight(.medium)
                    
                    if let section = viewModel.currentSection {
                        Text(section.title ?? "Untitled Section")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Text("\(Int(viewModel.progress * 100))% Complete")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            ProgressView(value: viewModel.progress)
                .progressViewStyle(LinearProgressViewStyle())
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
}

struct WizardSidebarView: View {
    @ObservedObject var viewModel: WizardViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            Text("Sections")
                .font(.headline)
                .padding()
            
            List {
                ForEach(Array(viewModel.sections.enumerated()), id: \.element.id) { sectionIndex, section in
                    WizardSectionRowView(
                        section: section,
                        sectionIndex: sectionIndex,
                        isCurrentSection: sectionIndex == viewModel.currentSectionIndex,
                        progress: viewModel.getSectionProgress(for: sectionIndex),
                        viewModel: viewModel
                    )
                }
            }
            .listStyle(SidebarListStyle())
        }
    }
}

struct WizardSectionRowView: View {
    let section: Section
    let sectionIndex: Int
    let isCurrentSection: Bool
    let progress: Double
    @ObservedObject var viewModel: WizardViewModel
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(section.title ?? "Untitled Section")
                    .font(.headline)
                    .foregroundColor(isCurrentSection ? .accentColor : .primary)
                    .lineLimit(2)
                
                Spacer()
                
                if progress >= 1.0 {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                }
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("\(section.questionsArray.count) questions")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle())
                
                Text("\(Int(progress * 100))% complete")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            // Question list for current section
            if isCurrentSection {
                VStack(alignment: .leading, spacing: 2) {
                    ForEach(Array(section.questionsArray.enumerated()), id: \.element.id) { questionIndex, question in
                        WizardQuestionRowView(
                            question: question,
                            questionIndex: questionIndex,
                            isCurrentQuestion: questionIndex == viewModel.currentQuestionIndex,
                            isAnswered: viewModel.isCurrentQuestionAnswered || viewModel.getAnswer(for: question.id!).isEmpty == false
                        ) {
                            viewModel.goToQuestion(sectionIndex: sectionIndex, questionIndex: questionIndex)
                        }
                    }
                }
                .padding(.leading, 8)
            }
        }
        .padding(.vertical, 4)
    }
}

struct WizardQuestionRowView: View {
    let question: Question
    let questionIndex: Int
    let isCurrentQuestion: Bool
    let isAnswered: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack {
                Text("\(questionIndex + 1).")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(width: 20, alignment: .leading)
                
                Text(question.text ?? "")
                    .font(.caption)
                    .foregroundColor(isCurrentQuestion ? .accentColor : .primary)
                    .lineLimit(1)
                
                Spacer()
                
                if isAnswered {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.caption)
                        .foregroundColor(.green)
                } else if question.isRequired {
                    Image(systemName: "asterisk")
                        .font(.caption2)
                        .foregroundColor(.red)
                }
            }
        }
        .buttonStyle(.plain)
        .padding(.vertical, 2)
    }
}

struct WizardQuestionView: View {
    @ObservedObject var viewModel: WizardViewModel
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                if let question = viewModel.currentQuestion {
                    VStack(alignment: .leading, spacing: 16) {
                        // Question header
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text(question.text ?? "")
                                    .font(.title2)
                                    .fontWeight(.medium)
                                
                                if question.isRequired {
                                    Text("*")
                                        .foregroundColor(.red)
                                        .font(.title2)
                                }
                            }
                            
                            Text(question.questionTypeEnum.rawValue.capitalized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(Color.accentColor.opacity(0.1))
                                .cornerRadius(4)
                        }
                        
                        // Question input
                        WizardQuestionInputView(
                            question: question,
                            answer: viewModel.getAnswer(for: question.id!),
                            onAnswerChanged: { newValue in
                                viewModel.updateAnswer(for: question.id!, value: newValue)
                            }
                        )
                    }
                    .padding()
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(12)
                } else {
                    Text("No question available")
                        .foregroundColor(.secondary)
                }
                
                Spacer(minLength: 100)
            }
            .padding()
        }
    }
}

struct WizardQuestionInputView: View {
    let question: Question
    let answer: String
    let onAnswerChanged: (String) -> Void
    
    @State private var localAnswer: String = ""
    @State private var selectedOptions: Set<String> = []
    @State private var selectedOption: String = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            switch question.questionTypeEnum {
            case .textInput:
                TextField("Enter your answer...", text: $localAnswer)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        onAnswerChanged(localAnswer)
                    }
                
            case .richText:
                VStack(alignment: .leading, spacing: 8) {
                    Text("Your answer:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextEditor(text: $localAnswer)
                        .frame(minHeight: 120)
                        .padding(8)
                        .background(Color(NSColor.textBackgroundColor))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                
            case .multipleChoice:
                VStack(alignment: .leading, spacing: 8) {
                    Text("Select all that apply:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ForEach(question.optionsArray, id: \.self) { option in
                        Button(action: {
                            if selectedOptions.contains(option) {
                                selectedOptions.remove(option)
                            } else {
                                selectedOptions.insert(option)
                            }
                            updateMultipleChoiceAnswer()
                        }) {
                            HStack {
                                Image(systemName: selectedOptions.contains(option) ? "checkmark.square.fill" : "square")
                                    .foregroundColor(selectedOptions.contains(option) ? .accentColor : .secondary)
                                Text(option)
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                        }
                        .buttonStyle(.plain)
                        .padding(.vertical, 4)
                    }
                }
                
            case .singleSelect:
                VStack(alignment: .leading, spacing: 8) {
                    Text("Select one:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ForEach(question.optionsArray, id: \.self) { option in
                        Button(action: {
                            selectedOption = option
                            onAnswerChanged(option)
                        }) {
                            HStack {
                                Image(systemName: selectedOption == option ? "largecircle.fill.circle" : "circle")
                                    .foregroundColor(selectedOption == option ? .accentColor : .secondary)
                                Text(option)
                                    .foregroundColor(.primary)
                                Spacer()
                            }
                        }
                        .buttonStyle(.plain)
                        .padding(.vertical, 4)
                    }
                }
                
            default:
                TextField("Enter your answer...", text: $localAnswer)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        onAnswerChanged(localAnswer)
                    }
            }
        }
        .onAppear {
            loadCurrentAnswer()
        }
        .onChange(of: localAnswer) { newValue in
            if question.questionTypeEnum == .richText {
                onAnswerChanged(newValue)
            }
        }
    }
    
    private func loadCurrentAnswer() {
        localAnswer = answer
        
        switch question.questionTypeEnum {
        case .multipleChoice:
            selectedOptions = Set(answer.components(separatedBy: ", ").filter { !$0.isEmpty })
        case .singleSelect:
            selectedOption = answer
        default:
            break
        }
    }
    
    private func updateMultipleChoiceAnswer() {
        let answerString = Array(selectedOptions).joined(separator: ", ")
        onAnswerChanged(answerString)
    }
}

struct WizardNavigationView: View {
    @ObservedObject var viewModel: WizardViewModel
    let onComplete: () -> Void
    
    var body: some View {
        HStack {
            Button("Previous") {
                viewModel.goToPrevious()
            }
            .disabled(!viewModel.canGoPrevious)
            
            Spacer()
            
            if let question = viewModel.currentQuestion, !question.isRequired {
                Button("Skip") {
                    viewModel.skipQuestion()
                }
                .buttonStyle(.borderless)
            }
            
            if viewModel.currentQuestionNumber == viewModel.totalQuestions {
                Button("Complete") {
                    onComplete()
                }
                .buttonStyle(.borderedProminent)
            } else {
                Button("Next") {
                    viewModel.goToNext()
                }
                .buttonStyle(.borderedProminent)
                .disabled(!viewModel.canGoNext)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
    }
}

struct WizardCompletionView: View {
    let document: PRDDocument
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("Document Complete!")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("You've successfully completed your PRD for \"\(document.title ?? "Untitled Document")\"")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 40)
            
            Button("Done") {
                onDismiss()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleDocument = PRDDocument(context: context)
    sampleDocument.title = "Sample Document"
    sampleDocument.projectType = "Learning & Development"
    
    return WizardView(document: sampleDocument, dataService: DataService.shared)
        .environment(\.managedObjectContext, context)
}