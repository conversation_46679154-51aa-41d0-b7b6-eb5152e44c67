import SwiftUI
import CoreData

struct DocumentDetailView: View {
    @ObservedObject var document: PRDDocument
    @StateObject private var dataService = DataService.shared
    @Environment(\.managedObjectContext) private var viewContext
    
    @State private var selectedSection: Section?
    @State private var showingDeleteAlert = false
    @State private var showingExportSheet = false
    @State private var showingWizard = false
    
    var body: some View {
        HSplitView {
            // Left Sidebar - Section Navigation
            VStack(alignment: .leading, spacing: 0) {
                // Document Header
                VStack(alignment: .leading, spacing: 8) {
                    Text(document.title ?? "Untitled Document")
                        .font(.title2)
                        .fontWeight(.bold)
                        .lineLimit(2)
                    
                    HStack {
                        Image(systemName: ProjectType(rawValue: document.projectType ?? "General")?.icon ?? "doc.text")
                            .foregroundColor(.accentColor)
                        Text(document.projectType ?? "General")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Progress Bar
                    ProgressView(value: document.completionPercentage)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text("\(Int(document.completionPercentage * 100))% Complete")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(NSColor.controlBackgroundColor))
                
                Divider()
                
                // Section List
                List(document.sectionsArray, id: \.id, selection: $selectedSection) { section in
                    SectionRowView(section: section)
                        .tag(section)
                }
                .listStyle(SidebarListStyle())
            }
            .frame(minWidth: 250, maxWidth: 350)
            
            // Right Content - Section Detail
            VStack {
                if let selectedSection = selectedSection {
                    SectionDetailView(section: selectedSection, dataService: dataService)
                } else {
                    DocumentOverviewView(document: document)
                }
            }
            .frame(minWidth: 500)
        }
        .navigationTitle(document.title ?? "Untitled Document")
        .navigationSubtitle(document.projectType ?? "General")
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                Button(action: { showingWizard = true }) {
                    Label("Guided Mode", systemImage: "wand.and.rays")
                }
                
                Button(action: { showingExportSheet = true }) {
                    Label("Export", systemImage: "square.and.arrow.up")
                }
                
                Menu {
                    Button("Duplicate Document") {
                        let _ = dataService.duplicateDocument(document)
                    }
                    
                    Divider()
                    
                    Button("Delete Document", role: .destructive) {
                        showingDeleteAlert = true
                    }
                } label: {
                    Label("More", systemImage: "ellipsis.circle")
                }
            }
        }
        .onAppear {
            // Select first section by default
            if selectedSection == nil && !document.sectionsArray.isEmpty {
                selectedSection = document.sectionsArray.first
            }
        }
        .alert("Delete Document", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                dataService.deleteDocument(document)
            }
        } message: {
            Text("Are you sure you want to delete this document? This action cannot be undone.")
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportView(document: document)
        }
        .sheet(isPresented: $showingWizard) {
            WizardView(document: document, dataService: dataService)
        }
    }
}

struct SectionRowView: View {
    @ObservedObject var section: Section
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(section.title ?? "Untitled Section")
                    .font(.headline)
                    .lineLimit(1)
                
                Text("\(section.questionsArray.count) questions")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                // Completion indicator
                if section.isCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Text("\(Int(section.completionPercentage * 100))%")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

struct DocumentOverviewView: View {
    @ObservedObject var document: PRDDocument
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 24) {
                // Header
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Image(systemName: ProjectType(rawValue: document.projectType ?? "General")?.icon ?? "doc.text")
                            .font(.system(size: 40))
                            .foregroundColor(.accentColor)
                        
                        VStack(alignment: .leading) {
                            Text(document.title ?? "Untitled Document")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                            
                            Text(document.projectType ?? "General")
                                .font(.title3)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    // Document metadata
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Created:")
                            Text(document.createdDate ?? Date(), style: .date)
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Last Modified:")
                            Text(document.modifiedDate ?? Date(), style: .relative)
                                .foregroundColor(.secondary)
                        }
                        
                        HStack {
                            Text("Progress:")
                            Text("\(Int(document.completionPercentage * 100))% Complete")
                                .foregroundColor(.secondary)
                        }
                    }
                    .font(.caption)
                }
                
                Divider()
                
                // Section Overview
                VStack(alignment: .leading, spacing: 16) {
                    Text("Sections Overview")
                        .font(.title2)
                        .fontWeight(.semibold)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 16) {
                        ForEach(document.sectionsArray, id: \.id) { section in
                            SectionOverviewCard(section: section)
                        }
                    }
                }
                
                Spacer()
            }
            .padding()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct SectionOverviewCard: View {
    @ObservedObject var section: Section
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(section.title ?? "Untitled Section")
                    .font(.headline)
                    .lineLimit(2)
                
                Spacer()
                
                if section.isCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                }
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("\(section.questionsArray.count) questions")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                ProgressView(value: section.completionPercentage)
                    .progressViewStyle(LinearProgressViewStyle())
                
                Text("\(Int(section.completionPercentage * 100))% complete")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleDocument = PRDDocument(context: context)
    sampleDocument.title = "Sample L&D Platform"
    sampleDocument.projectType = "Learning & Development"
    sampleDocument.createdDate = Date()
    sampleDocument.modifiedDate = Date()
    
    return DocumentDetailView(document: sampleDocument)
        .environment(\.managedObjectContext, context)
}