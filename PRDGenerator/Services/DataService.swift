import Foundation
import CoreData

class DataService: ObservableObject {
    static let shared = DataService()
    
    private let persistenceController = PersistenceController.shared
    
    var viewContext: NSManagedObjectContext {
        return persistenceController.container.viewContext
    }
    
    private init() {}
    
    // MARK: - PRD Document Operations
    
    func createDocument(title: String, projectType: ProjectType) -> PRDDocument {
        let document = PRDDocument(context: viewContext)
        document.id = UUID()
        document.title = title
        document.projectType = projectType.rawValue
        document.createdDate = Date()
        document.modifiedDate = Date()
        document.isCompleted = false
        
        // Apply template
        document.applyTemplate(projectType: projectType)
        
        saveContext()
        return document
    }
    
    func updateDocument(_ document: PRDDocument) {
        document.updateModifiedDate()
        saveContext()
    }
    
    func deleteDocument(_ document: PRDDocument) {
        viewContext.delete(document)
        saveContext()
    }
    
    func duplicateDocument(_ document: PRDDocument) -> PRDDocument {
        let newDocument = PRDDocument(context: viewContext)
        newDocument.id = UUID()
        newDocument.title = "\(document.title ?? "Untitled") Copy"
        newDocument.projectType = document.projectType
        newDocument.createdDate = Date()
        newDocument.modifiedDate = Date()
        newDocument.isCompleted = false
        
        // Copy sections and questions
        for section in document.sectionsArray {
            let newSection = newDocument.addSection(
                title: section.title ?? "",
                sectionType: section.sectionType ?? "",
                order: section.order
            )
            newSection.content = section.content
            
            for question in section.questionsArray {
                let newQuestion = newSection.addQuestion(
                    text: question.text ?? "",
                    questionType: question.questionType ?? "textInput",
                    isRequired: question.isRequired,
                    order: question.order
                )
                newQuestion.options = question.options
                
                // Copy answer if exists
                if let originalAnswer = question.answer, let value = originalAnswer.value {
                    newQuestion.updateAnswer(value: value)
                }
            }
        }
        
        saveContext()
        return newDocument
    }
    
    // MARK: - Section Operations
    
    func addSection(to document: PRDDocument, title: String, sectionType: String) -> Section {
        let maxOrder = document.sectionsArray.map { $0.order }.max() ?? 0
        let section = document.addSection(
            title: title,
            sectionType: sectionType,
            order: maxOrder + 1
        )
        saveContext()
        return section
    }
    
    func updateSection(_ section: Section, title: String? = nil, content: String? = nil) {
        if let title = title {
            section.title = title
        }
        if let content = content {
            section.content = content
        }
        section.updateDocument()
        saveContext()
    }
    
    func deleteSection(_ section: Section) {
        viewContext.delete(section)
        saveContext()
    }
    
    // MARK: - Question and Answer Operations
    
    func updateAnswer(for question: Question, value: String) {
        question.updateAnswer(value: value)
        saveContext()
    }
    
    func addQuestion(to section: Section, text: String, questionType: QuestionType, isRequired: Bool = false) -> Question {
        let maxOrder = section.questionsArray.map { $0.order }.max() ?? 0
        let question = section.addQuestion(
            text: text,
            questionType: questionType.rawValue,
            isRequired: isRequired,
            order: maxOrder + 1
        )
        saveContext()
        return question
    }
    
    // MARK: - Core Data Operations
    
    func saveContext() {
        guard viewContext.hasChanges else { return }
        
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            print("Failed to save context: \(nsError), \(nsError.userInfo)")
        }
    }
    
    func fetchDocuments() -> [PRDDocument] {
        let request: NSFetchRequest<PRDDocument> = PRDDocument.fetchRequest()
        request.sortDescriptors = [NSSortDescriptor(keyPath: \PRDDocument.modifiedDate, ascending: false)]
        
        do {
            return try viewContext.fetch(request)
        } catch {
            print("Failed to fetch documents: \(error)")
            return []
        }
    }
    
    // MARK: - Auto-save functionality
    
    private var autoSaveTimer: Timer?
    
    func startAutoSave() {
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { _ in
            self.saveContext()
        }
    }
    
    func stopAutoSave() {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
    }
}