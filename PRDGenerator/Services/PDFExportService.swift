import Foundation
import PDFKit
import AppKit
import CoreData

class PDFExportService {
    static let shared = PDFExportService()
    
    private init() {}
    
    // MARK: - PDF Generation
    
    func generatePDF(for document: PRDDocument) -> PDFDocument? {
        let content = PRDGenerationService.shared.generatePRDContent(for: document, format: .html)
        
        // Create HTML attributed string
        guard let htmlData = content.data(using: .utf8) else {
            return nil
        }
        
        let options: [NSAttributedString.DocumentReadingOptionKey: Any] = [
            .documentType: NSAttributedString.DocumentType.html,
            .characterEncoding: String.Encoding.utf8.rawValue
        ]
        
        guard let attributedString = try? NSAttributedString(
            data: htmlData,
            options: options,
            documentAttributes: nil
        ) else {
            return nil
        }
        
        // Create PDF document
        let pdfDocument = PDFDocument()
        
        // Create a text view to render the content
        let textView = NSTextView(frame: NSRect(x: 0, y: 0, width: 612, height: 792))
        textView.textStorage?.setAttributedString(attributedString)
        
        // Configure print info
        let printInfo = NSPrintInfo.shared
        printInfo.paperSize = NSMakeSize(612, 792)
        printInfo.topMargin = 72
        printInfo.bottomMargin = 72
        printInfo.leftMargin = 72
        printInfo.rightMargin = 72
        
        // Create PDF from the text view
        let operation = NSPrintOperation(view: textView, printInfo: printInfo)
        operation.showsPrintPanel = false
        operation.showsProgressPanel = false
        
        let pdfData = NSMutableData()
        let printSettings: [NSPrintInfo.AttributeKey: Any] = [
            .jobDisposition: NSPrintInfo.JobDisposition.save,
            .jobSavingURL: FileManager.default.temporaryDirectory
        ]
        
        operation.printInfo.dictionary().addEntries(from: printSettings)
        
        // This is a simplified approach - in practice, you might need to use
        // Core Graphics directly for more control
        return createPDFDocument(from: attributedString, pageSize: NSMakeSize(612, 792))
    }
    
    // MARK: - Advanced PDF Creation
    
    private func createPDFDocument(from attributedString: NSAttributedString, pageSize: NSSize) -> PDFDocument {
        let pdfDocument = PDFDocument()
        
        // Create a text view to measure and layout text
        let textView = NSTextView(frame: NSRect(x: 0, y: 0, width: pageSize.width - 144, height: pageSize.height - 144))
        textView.textStorage?.setAttributedString(attributedString)
        
        // Calculate how many pages we need
        let layoutManager = textView.layoutManager!
        let textContainer = textView.textContainer!
        
        var pageIndex = 0
        var currentLocation = 0
        
        while currentLocation < attributedString.length {
            let page = PDFPage()
            let bounds = NSRect(x: 72, y: 72, width: pageSize.width - 144, height: pageSize.height - 144)
            
            // Create a new text view for this page
            let pageTextView = NSTextView(frame: bounds)
            pageTextView.textContainer?.containerSize = bounds.size
            pageTextView.textContainer?.widthTracksTextView = true
            
            // Calculate the range for this page
            let glyphRange = layoutManager.glyphRange(for: textContainer)
            let charRange = layoutManager.characterRange(forGlyphRange: glyphRange, actualGlyphRange: nil)
            
            if charRange.location < currentLocation {
                break
            }
            
            let pageRange = NSRange(location: currentLocation, length: min(1000, attributedString.length - currentLocation))
            
            // Create attributed string for this page
            let pageString = attributedString.attributedSubstring(from: pageRange)
            pageTextView.textStorage?.setAttributedString(pageString)
            
            // Add page to document
            pdfDocument.insert(page, at: pageIndex)
            pageIndex += 1
            currentLocation += pageRange.length
        }
        
        return pdfDocument
    }
    
    // MARK: - Simple PDF Generation
    
    func generateSimplePDF(for document: PRDDocument) -> PDFDocument {
        let pdfDocument = PDFDocument()
        let pageSize = CGSize(width: 612, height: 792)
        let margins: CGFloat = 72
        
        // Create content
        let content = PRDGenerationService.shared.generatePRDContent(for: document, format: .plainText)
        
        // Create attributed string
        let font = NSFont.systemFont(ofSize: 12)
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: NSColor.black
        ]
        
        let attributedString = NSAttributedString(string: content, attributes: attributes)
        
        // Create text view for layout
        let textView = NSTextView(frame: NSRect(
            x: margins,
            y: margins,
            width: pageSize.width - 2 * margins,
            height: pageSize.height - 2 * margins
        ))
        
        textView.textStorage?.setAttributedString(attributedString)
        
        // Simple single-page PDF
        let page = PDFPage()
        let bounds = NSRect(x: 0, y: 0, width: pageSize.width, height: pageSize.height)
        
        // Create PDF representation
        let pdfData = NSMutableData()
        let consumer = CGDataConsumer(data: pdfData as CFMutableData)!
        var mediaBox = bounds
        let ctx = CGContext(consumer: consumer, mediaBox: &mediaBox, nil)!
        
        ctx.beginPDFPage(nil)
        
        // Draw background
        ctx.setFillColor(NSColor.white.cgColor)
        ctx.fill(bounds)
        
        // Draw text
        let textRect = NSRect(
            x: margins,
            y: margins,
            width: pageSize.width - 2 * margins,
            height: pageSize.height - 2 * margins
        )
        
        attributedString.draw(in: textRect)
        
        ctx.endPDFPage()
        ctx.closePDF()
        
        return PDFDocument(data: pdfData as Data) ?? PDFDocument()
    }
    
    // MARK: - Helper Methods
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "Unknown" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}