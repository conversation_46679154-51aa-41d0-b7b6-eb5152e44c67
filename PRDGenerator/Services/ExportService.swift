import Foundation
import AppKit

class ExportService {
    static let shared = ExportService()
    
    private init() {}
    
    // MARK: - Export Methods
    
    func exportDocument(_ document: PRDDocument, format: ExportFormat, completion: @escaping (Result<URL, Error>) -> Void) {
        let content = PRDGenerationService.shared.generatePRDContent(for: document, format: format)
        
        switch format {
        case .pdf:
            exportToPDF(document: document, completion: completion)
        case .html:
            exportToHTML(content: content, document: document, completion: completion)
        case .rtf:
            exportToRTF(content: content, document: document, completion: completion)
        case .word:
            exportToWord(content: content, document: document, completion: completion)
        case .markdown:
            exportToMarkdown(content: content, document: document, completion: completion)
        case .plainText:
            exportToPlainText(content: content, document: document, completion: completion)
        }
    }
    
    // MARK: - PDF Export
    
    private func exportToPDF(document: PRDDocument, completion: @escaping (Result<URL, Error>) -> Void) {
        DispatchQueue.global(qos: .userInitiated).async {
            guard let pdfDocument = PDFExportService.shared.generatePDF(for: document) else {
                DispatchQueue.main.async {
                    completion(.failure(ExportError.pdfGenerationFailed))
                }
                return
            }
            
            let fileName = "\(document.title ?? "PRD").pdf"
            let url = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
            
            if pdfDocument.write(to: url) {
                DispatchQueue.main.async {
                    completion(.success(url))
                }
            } else {
                DispatchQueue.main.async {
                    completion(.failure(ExportError.fileWriteFailed))
                }
            }
        }
    }
    
    // MARK: - HTML Export
    
    private func exportToHTML(content: String, document: PRDDocument, completion: @escaping (Result<URL, Error>) -> Void) {
        let styledHTML = generateStyledHTML(content: content, document: document)
        
        let fileName = "\(document.title ?? "PRD").html"
        let url = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        do {
            try styledHTML.write(to: url, atomically: true, encoding: .utf8)
            completion(.success(url))
        } catch {
            completion(.failure(error))
        }
    }
    
    private func generateStyledHTML(content: String, document: PRDDocument) -> String {
        let css = """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.8em;
            font-weight: 400;
        }
        
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 500;
        }
        
        .document-meta {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .document-meta p {
            margin: 5px 0;
            font-size: 0.95em;
        }
        
        p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        ul, ol {
            margin-bottom: 20px;
            padding-left: 30px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding: 10px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.9em;
        }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.95em;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        """
        
        return """
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>\(document.title ?? "PRD Document")</title>
            <style>
            \(css)
            </style>
        </head>
        <body>
            <div class="container">
                \(content)
            </div>
        </body>
        </html>
        """
    }
    
    // MARK: - RTF Export
    
    private func exportToRTF(content: String, document: PRDDocument, completion: @escaping (Result<URL, Error>) -> Void) {
        let rtfContent = generateRTFContent(content: content, document: document)
        
        let fileName = "\(document.title ?? "PRD").rtf"
        let url = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        do {
            try rtfContent.write(to: url, atomically: true, encoding: .utf8)
            completion(.success(url))
        } catch {
            completion(.failure(error))
        }
    }
    
    private func generateRTFContent(content: String, document: PRDDocument) -> String {
        let rtfHeader = """
        {\\rtf1\\ansi\\deff0
        {\\fonttbl{\\f0\\fswiss Helvetica;}{\\f1\\fmodern Courier New;}}
        {\\colortbl;\\red0\\green0\\blue0;\\red0\\green0\\blue255;\\red255\\green0\\blue0;}
        \\paperw11906\\paperh16838\\margl1440\\margr1440\\margt1440\\margb1440
        """
        
        let title = "{\\b\\fs36\\qc \(document.title ?? "PRD Document")\\par}"
        let meta = "{\\pard\\qc Project Type: \(document.projectType ?? "General")\\par}"
        let body = convertMarkdownToRTF(content)
        
        return rtfHeader + title + meta + body + "}"
    }
    
    private func convertMarkdownToRTF(_ markdown: String) -> String {
        var rtf = markdown
        
        // Basic markdown to RTF conversion
        rtf = rtf.replacingOccurrences(of: "# ", with: "\\par{\\b\\fs24 ")
        rtf = rtf.replacingOccurrences(of: "## ", with: "\\par{\\b\\fs20 ")
        rtf = rtf.replacingOccurrences(of: "### ", with: "\\par{\\b\\fs18 ")
        rtf = rtf.replacingOccurrences(of: "\n\n", with: "\\par}")
        rtf = rtf.replacingOccurrences(of: "**", with: "{\\b ")
        rtf = rtf.replacingOccurrences(of: "**", with: "}")
        rtf = rtf.replacingOccurrences(of: "*", with: "{\\i ")
        rtf = rtf.replacingOccurrences(of: "*", with: "}")
        
        return rtf
    }
    
    // MARK: - Word Export (RTF format)
    
    private func exportToWord(content: String, document: PRDDocument, completion: @escaping (Result<URL, Error>) -> Void) {
        // Word export uses RTF format for compatibility
        exportToRTF(content: content, document: document, completion: completion)
    }
    
    // MARK: - Markdown Export
    
    private func exportToMarkdown(content: String, document: PRDDocument, completion: @escaping (Result<URL, Error>) -> Void) {
        let fileName = "\(document.title ?? "PRD").md"
        let url = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        do {
            try content.write(to: url, atomically: true, encoding: .utf8)
            completion(.success(url))
        } catch {
            completion(.failure(error))
        }
    }
    
    // MARK: - Plain Text Export
    
    private func exportToPlainText(content: String, document: PRDDocument, completion: @escaping (Result<URL, Error>) -> Void) {
        let plainText = stripHTML(from: content)
        let fileName = "\(document.title ?? "PRD").txt"
        let url = FileManager.default.temporaryDirectory.appendingPathComponent(fileName)
        
        do {
            try plainText.write(to: url, atomically: true, encoding: .utf8)
            completion(.success(url))
        } catch {
            completion(.failure(error))
        }
    }
    
    private func stripHTML(from content: String) -> String {
        var plainText = content
        
        // Remove HTML tags
        plainText = plainText.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        
        // Convert HTML entities
        plainText = plainText.replacingOccurrences(of: "&nbsp;", with: " ")
        plainText = plainText.replacingOccurrences(of: "&", with: "&")
        plainText = plainText.replacingOccurrences(of: "<", with: "<")
        plainText = plainText.replacingOccurrences(of: ">", with: ">")
        
        return plainText
    }
}

// MARK: - Export Format Enum

enum ExportFormat: String, CaseIterable {
    case pdf = "PDF"
    case html = "HTML"
    case rtf = "RTF"
    case word = "Word"
    case markdown = "Markdown"
    case plainText = "Plain Text"
    
    var fileExtension: String {
        switch self {
        case .pdf: return "pdf"
        case .html: return "html"
        case .rtf: return "rtf"
        case .word: return "doc"
        case .markdown: return "md"
        case .plainText: return "txt"
        }
    }
    
    var mimeType: String {
        switch self {
        case .pdf: return "application/pdf"
        case .html: return "text/html"
        case .rtf: return "application/rtf"
        case .word: return "application/msword"
        case .markdown: return "text/markdown"
        case .plainText: return "text/plain"
        }
    }
}

// MARK: - Export Error

enum ExportError: Error {
    case pdfGenerationFailed
    case fileWriteFailed
    case invalidContent
    
    var localizedDescription: String {
        switch self {
        case .pdfGenerationFailed:
            return "Failed to generate PDF document"
        case .fileWriteFailed:
            return "Failed to write file to disk"
        case .invalidContent:
            return "Invalid content for export"
        }
    }
}