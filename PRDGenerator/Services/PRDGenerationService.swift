import Foundation
import CoreData

class PRDGenerationService {
    static let shared = PRDGenerationService()
    
    private init() {}
    
    // MARK: - Document Generation
    
    func generatePRDContent(for document: PRDDocument, format: ExportFormat = .markdown) -> String {
        var content = ""
        
        // Add document header
        content += generateDocumentHeader(document, format: format)
        content += "\n\n"
        
        // Add table of contents
        content += generateTableOfContents(document, format: format)
        content += "\n\n"
        
        // Add executive summary
        content += generateExecutiveSummary(document, format: format)
        content += "\n\n"
        
        // Add sections
        for section in document.sectionsArray {
            content += generateSectionContent(section, format: format)
            content += "\n\n"
        }
        
        // Add appendices
        content += generateAppendices(document, format: format)
        
        return content
    }
    
    // MARK: - Document Header
    
    private func generateDocumentHeader(_ document: PRDDocument, format: ExportFormat) -> String {
        var header = ""
        
        switch format {
        case .markdown:
            header += "# \(document.title ?? "Product Requirements Document")\n\n"
            header += "**Project Type:** \(document.projectType ?? "General")\n\n"
            header += "**Document Version:** 1.0\n\n"
            header += "**Last Updated:** \(formatDate(document.modifiedDate))\n\n"
            header += "**Status:** \(document.isCompleted ? "Complete" : "In Progress")\n\n"
            
        case .html:
            header += "<h1>\(document.title ?? "Product Requirements Document")</h1>\n"
            header += "<div class=\"document-meta\">\n"
            header += "<p><strong>Project Type:</strong> \(document.projectType ?? "General")</p>\n"
            header += "<p><strong>Document Version:</strong> 1.0</p>\n"
            header += "<p><strong>Last Updated:</strong> \(formatDate(document.modifiedDate))</p>\n"
            header += "<p><strong>Status:</strong> \(document.isCompleted ? "Complete" : "In Progress")</p>\n"
            header += "</div>\n"
            
        case .plainText:
            header += "\(document.title ?? "Product Requirements Document")\n"
            header += String(repeating: "=", count: (document.title ?? "Product Requirements Document").count) + "\n\n"
            header += "Project Type: \(document.projectType ?? "General")\n"
            header += "Document Version: 1.0\n"
            header += "Last Updated: \(formatDate(document.modifiedDate))\n"
            header += "Status: \(document.isCompleted ? "Complete" : "In Progress")\n"
        }
        
        return header
    }
    
    // MARK: - Table of Contents
    
    private func generateTableOfContents(_ document: PRDDocument, format: ExportFormat) -> String {
        var toc = ""
        
        switch format {
        case .markdown:
            toc += "## Table of Contents\n\n"
            toc += "1. [Executive Summary](#executive-summary)\n"
            
            for (index, section) in document.sectionsArray.enumerated() {
                let sectionTitle = section.title ?? "Untitled Section"
                let anchor = sectionTitle.lowercased().replacingOccurrences(of: " ", with: "-")
                toc += "\(index + 2). [\(sectionTitle)](#\(anchor))\n"
            }
            
            toc += "\(document.sectionsArray.count + 2). [Appendices](#appendices)\n"
            
        case .html:
            toc += "<h2>Table of Contents</h2>\n<ol>\n"
            toc += "<li><a href=\"#executive-summary\">Executive Summary</a></li>\n"
            
            for section in document.sectionsArray {
                let sectionTitle = section.title ?? "Untitled Section"
                let anchor = sectionTitle.lowercased().replacingOccurrences(of: " ", with: "-")
                toc += "<li><a href=\"#\(anchor)\">\(sectionTitle)</a></li>\n"
            }
            
            toc += "<li><a href=\"#appendices\">Appendices</a></li>\n</ol>\n"
            
        case .plainText:
            toc += "Table of Contents\n"
            toc += "-----------------\n\n"
            toc += "1. Executive Summary\n"
            
            for (index, section) in document.sectionsArray.enumerated() {
                toc += "\(index + 2). \(section.title ?? "Untitled Section")\n"
            }
            
            toc += "\(document.sectionsArray.count + 2). Appendices\n"
        }
        
        return toc
    }
    
    // MARK: - Executive Summary
    
    private func generateExecutiveSummary(_ document: PRDDocument, format: ExportFormat) -> String {
        var summary = ""
        
        // Extract key information from the first section (usually Project Overview)
        let overviewSection = document.sectionsArray.first
        let projectDescription = extractProjectDescription(from: overviewSection)
        let targetAudience = extractTargetAudience(from: overviewSection)
        let keyObjectives = extractKeyObjectives(from: document)
        
        switch format {
        case .markdown:
            summary += "## Executive Summary\n\n"
            summary += "### Project Overview\n"
            summary += "\(projectDescription)\n\n"
            summary += "### Target Audience\n"
            summary += "\(targetAudience)\n\n"
            summary += "### Key Objectives\n"
            summary += keyObjectives
            
        case .html:
            summary += "<h2 id=\"executive-summary\">Executive Summary</h2>\n"
            summary += "<h3>Project Overview</h3>\n"
            summary += "<p>\(projectDescription)</p>\n"
            summary += "<h3>Target Audience</h3>\n"
            summary += "<p>\(targetAudience)</p>\n"
            summary += "<h3>Key Objectives</h3>\n"
            summary += keyObjectives
            
        case .plainText:
            summary += "Executive Summary\n"
            summary += "=================\n\n"
            summary += "Project Overview\n"
            summary += "----------------\n"
            summary += "\(projectDescription)\n\n"
            summary += "Target Audience\n"
            summary += "---------------\n"
            summary += "\(targetAudience)\n\n"
            summary += "Key Objectives\n"
            summary += "--------------\n"
            summary += keyObjectives
        }
        
        return summary
    }
    
    // MARK: - Section Content Generation
    
    private func generateSectionContent(_ section: Section, format: ExportFormat) -> String {
        var content = ""
        
        let sectionTitle = section.title ?? "Untitled Section"
        let anchor = sectionTitle.lowercased().replacingOccurrences(of: " ", with: "-")
        
        switch format {
        case .markdown:
            content += "## \(sectionTitle)\n\n"
            
        case .html:
            content += "<h2 id=\"\(anchor)\">\(sectionTitle)</h2>\n"
            
        case .plainText:
            content += "\(sectionTitle)\n"
            content += String(repeating: "=", count: sectionTitle.count) + "\n\n"
        }
        
        // Add section description if available
        if let sectionContent = section.content, !sectionContent.isEmpty {
            content += "\(sectionContent)\n\n"
        }
        
        // Group questions by type for better organization
        let groupedQuestions = groupQuestionsByType(section.questionsArray)
        
        for (questionType, questions) in groupedQuestions {
            if questions.count > 1 {
                content += generateQuestionGroupContent(questions, type: questionType, format: format)
            } else {
                content += generateQuestionContent(questions.first!, format: format)
            }
            content += "\n"
        }
        
        return content
    }
    
    private func generateQuestionContent(_ question: Question, format: ExportFormat) -> String {
        var content = ""
        
        let questionText = question.text ?? ""
        let answer = question.answer?.value ?? ""
        
        if answer.isEmpty { return "" }
        
        switch format {
        case .markdown:
            content += "### \(questionText)\n\n"
            content += formatAnswerContent(answer, questionType: question.questionTypeEnum, format: format)
            
        case .html:
            content += "<h3>\(questionText)</h3>\n"
            content += formatAnswerContent(answer, questionType: question.questionTypeEnum, format: format)
            
        case .plainText:
            content += "\(questionText)\n"
            content += String(repeating: "-", count: min(questionText.count, 50)) + "\n"
            content += formatAnswerContent(answer, questionType: question.questionTypeEnum, format: format)
        }
        
        return content + "\n"
    }
    
    private func generateQuestionGroupContent(_ questions: [Question], type: QuestionType, format: ExportFormat) -> String {
        var content = ""
        
        let groupTitle = getGroupTitle(for: type)
        
        switch format {
        case .markdown:
            content += "### \(groupTitle)\n\n"
            
        case .html:
            content += "<h3>\(groupTitle)</h3>\n"
            
        case .plainText:
            content += "\(groupTitle)\n"
            content += String(repeating: "-", count: groupTitle.count) + "\n\n"
        }
        
        for question in questions {
            let answer = question.answer?.value ?? ""
            if !answer.isEmpty {
                switch format {
                case .markdown:
                    content += "**\(question.text ?? ""):** \(answer)\n\n"
                case .html:
                    content += "<p><strong>\(question.text ?? ""):</strong> \(answer)</p>\n"
                case .plainText:
                    content += "\(question.text ?? ""): \(answer)\n\n"
                }
            }
        }
        
        return content
    }
    
    // MARK: - Answer Formatting
    
    private func formatAnswerContent(_ answer: String, questionType: QuestionType, format: ExportFormat) -> String {
        switch questionType {
        case .multipleChoice:
            return formatMultipleChoiceAnswer(answer, format: format)
        case .richText:
            return formatRichTextAnswer(answer, format: format)
        case .urlInput:
            return formatURLAnswer(answer, format: format)
        case .emailInput:
            return formatEmailAnswer(answer, format: format)
        default:
            return answer + "\n\n"
        }
    }
    
    private func formatMultipleChoiceAnswer(_ answer: String, format: ExportFormat) -> String {
        let options = answer.components(separatedBy: ", ")
        var formatted = ""
        
        switch format {
        case .markdown:
            for option in options {
                formatted += "- \(option)\n"
            }
            formatted += "\n"
            
        case .html:
            formatted += "<ul>\n"
            for option in options {
                formatted += "<li>\(option)</li>\n"
            }
            formatted += "</ul>\n"
            
        case .plainText:
            for option in options {
                formatted += "• \(option)\n"
            }
            formatted += "\n"
        }
        
        return formatted
    }
    
    private func formatRichTextAnswer(_ answer: String, format: ExportFormat) -> String {
        // For now, return as-is. In the future, could parse markdown or HTML
        return answer + "\n\n"
    }
    
    private func formatURLAnswer(_ answer: String, format: ExportFormat) -> String {
        switch format {
        case .markdown:
            return "[\(answer)](\(answer))\n\n"
        case .html:
            return "<a href=\"\(answer)\">\(answer)</a>\n\n"
        case .plainText:
            return "\(answer)\n\n"
        }
    }
    
    private func formatEmailAnswer(_ answer: String, format: ExportFormat) -> String {
        switch format {
        case .markdown:
            return "[\(answer)](mailto:\(answer))\n\n"
        case .html:
            return "<a href=\"mailto:\(answer)\">\(answer)</a>\n\n"
        case .plainText:
            return "\(answer)\n\n"
        }
    }
    
    // MARK: - Appendices
    
    private func generateAppendices(_ document: PRDDocument, format: ExportFormat) -> String {
        var appendices = ""
        
        switch format {
        case .markdown:
            appendices += "## Appendices\n\n"
            appendices += "### Document Information\n\n"
            appendices += "- **Created:** \(formatDate(document.createdDate))\n"
            appendices += "- **Last Modified:** \(formatDate(document.modifiedDate))\n"
            appendices += "- **Completion Status:** \(Int(document.completionPercentage * 100))%\n"
            appendices += "- **Total Sections:** \(document.sectionsArray.count)\n"
            appendices += "- **Total Questions:** \(document.sectionsArray.reduce(0) { $0 + $1.questionsArray.count })\n\n"
            
        case .html:
            appendices += "<h2 id=\"appendices\">Appendices</h2>\n"
            appendices += "<h3>Document Information</h3>\n"
            appendices += "<ul>\n"
            appendices += "<li><strong>Created:</strong> \(formatDate(document.createdDate))</li>\n"
            appendices += "<li><strong>Last Modified:</strong> \(formatDate(document.modifiedDate))</li>\n"
            appendices += "<li><strong>Completion Status:</strong> \(Int(document.completionPercentage * 100))%</li>\n"
            appendices += "<li><strong>Total Sections:</strong> \(document.sectionsArray.count)</li>\n"
            appendices += "<li><strong>Total Questions:</strong> \(document.sectionsArray.reduce(0) { $0 + $1.questionsArray.count })</li>\n"
            appendices += "</ul>\n"
            
        case .plainText:
            appendices += "Appendices\n"
            appendices += "==========\n\n"
            appendices += "Document Information\n"
            appendices += "-------------------\n"
            appendices += "Created: \(formatDate(document.createdDate))\n"
            appendices += "Last Modified: \(formatDate(document.modifiedDate))\n"
            appendices += "Completion Status: \(Int(document.completionPercentage * 100))%\n"
            appendices += "Total Sections: \(document.sectionsArray.count)\n"
            appendices += "Total Questions: \(document.sectionsArray.reduce(0) { $0 + $1.questionsArray.count })\n"
        }
        
        return appendices
    }
    
    // MARK: - Helper Methods
    
    private func extractProjectDescription(from section: Section?) -> String {
        guard let section = section else { return "No project description available." }
        
        // Look for questions about project purpose or description
        for question in section.questionsArray {
            let questionText = question.text?.lowercased() ?? ""
            if questionText.contains("purpose") || questionText.contains("description") || questionText.contains("what") {
                return question.answer?.value ?? "No description provided."
            }
        }
        
        return "No project description available."
    }
    
    private func extractTargetAudience(from section: Section?) -> String {
        guard let section = section else { return "Target audience not specified." }
        
        for question in section.questionsArray {
            let questionText = question.text?.lowercased() ?? ""
            if questionText.contains("target") || questionText.contains("audience") || questionText.contains("users") {
                return question.answer?.value ?? "Target audience not specified."
            }
        }
        
        return "Target audience not specified."
    }
    
    private func extractKeyObjectives(from document: PRDDocument) -> String {
        var objectives = ""
        
        // Look through all sections for objective-related questions
        for section in document.sectionsArray {
            for question in section.questionsArray {
                let questionText = question.text?.lowercased() ?? ""
                if questionText.contains("objective") || questionText.contains("goal") || questionText.contains("success") {
                    if let answer = question.answer?.value, !answer.isEmpty {
                        objectives += "• \(answer)\n"
                    }
                }
            }
        }
        
        return objectives.isEmpty ? "No specific objectives defined." : objectives
    }
    
    private func groupQuestionsByType(_ questions: [Question]) -> [QuestionType: [Question]] {
        return Dictionary(grouping: questions) { $0.questionTypeEnum }
    }
    
    private func getGroupTitle(for questionType: QuestionType) -> String {
        switch questionType {
        case .textInput:
            return "Key Information"
        case .richText:
            return "Detailed Descriptions"
        case .multipleChoice:
            return "Selected Options"
        case .singleSelect:
            return "Preferences"
        case .dateInput:
            return "Important Dates"
        case .numberInput:
            return "Metrics & Numbers"
        case .urlInput:
            return "Related Links"
        case .emailInput:
            return "Contact Information"
        }
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "Unknown" }
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

// MARK: - Export Format Enum

enum ExportFormat: String, CaseIterable {
    case markdown = "Markdown"
    case html = "HTML"
    case plainText = "Plain Text"
    case pdf = "PDF"
    case rtf = "RTF"
    case word = "Word"
    
    var fileExtension: String {
        switch self {
        case .markdown: return "md"
        case .html: return "html"
        case .plainText: return "txt"
        case .pdf: return "pdf"
        case .rtf: return "rtf"
        case .word: return "doc"
        }
    }
    
    var mimeType: String {
        switch self {
        case .markdown: return "text/markdown"
        case .html: return "text/html"
        case .plainText: return "text/plain"
        case .pdf: return "application/pdf"
        case .rtf: return "application/rtf"
        case .word: return "application/msword"
        }
    }
}