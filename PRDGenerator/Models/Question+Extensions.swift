import Foundation
import CoreData

// MARK: - Question Extensions
extension Question {
    
    // MARK: - Computed Properties
    var questionTypeEnum: QuestionType {
        return QuestionType(rawValue: questionType ?? "textInput") ?? .textInput
    }
    
    var optionsArray: [String] {
        guard let options = options, !options.isEmpty else { return [] }
        return options.components(separatedBy: "|")
    }
    
    var isAnswered: Bool {
        guard let answer = answer else { return false }
        return !(answer.value?.isEmpty ?? true)
    }
    
    // MARK: - Helper Methods
    func updateAnswer(value: String) {
        if answer == nil {
            let newAnswer = Answer(context: managedObjectContext!)
            newAnswer.id = UUID()
            newAnswer.question = self
            answer = newAnswer
        }
        
        answer?.value = value
        section?.updateDocument()
    }
    
    func setOptions(_ optionsList: [String]) {
        options = optionsList.joined(separator: "|")
    }
}