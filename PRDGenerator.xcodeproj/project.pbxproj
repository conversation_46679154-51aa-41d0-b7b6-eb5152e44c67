// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		A1234567890123456789001 /* PRDGeneratorApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789002 /* PRDGeneratorApp.swift */; };
		A1234567890123456789003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789004 /* ContentView.swift */; };
		A1234567890123456789005 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789006 /* Assets.xcassets */; };
		A1234567890123456789007 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789008 /* Preview Assets.xcassets */; };
		A1234567890123456789009 /* PRDGenerator.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789010 /* PRDGenerator.xcdatamodeld */; };
		A1234567890123456789011 /* PersistenceController.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789012 /* PersistenceController.swift */; };
		A1234567890123456789013 /* DataService.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789014 /* DataService.swift */; };
		A1234567890123456789015 /* WizardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789016 /* WizardViewModel.swift */; };
		A1234567890123456789017 /* DocumentDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789018 /* DocumentDetailView.swift */; };
		A1234567890123456789019 /* WizardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789020 /* WizardView.swift */; };
		A1234567890123456789021 /* NewDocumentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789022 /* NewDocumentView.swift */; };
		A1234567890123456789023 /* ExportView.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789024 /* ExportView.swift */; };
		A1234567890123456789025 /* ProjectTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = A1234567890123456789026 /* ProjectTypes.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		A1234567890123456789100 /* PRDGenerator.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PRDGenerator.app; sourceTree = BUILT_PRODUCTS_DIR; };
		A1234567890123456789002 /* PRDGeneratorApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PRDGeneratorApp.swift; sourceTree = "<group>"; };
		A1234567890123456789004 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		A1234567890123456789006 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		A1234567890123456789008 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		A1234567890123456789010 /* PRDGenerator.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = PRDGenerator.xcdatamodel; sourceTree = "<group>"; };
		A1234567890123456789012 /* PersistenceController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersistenceController.swift; sourceTree = "<group>"; };
		A1234567890123456789014 /* DataService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataService.swift; sourceTree = "<group>"; };
		A1234567890123456789016 /* WizardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WizardViewModel.swift; sourceTree = "<group>"; };
		A1234567890123456789018 /* DocumentDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentDetailView.swift; sourceTree = "<group>"; };
		A1234567890123456789020 /* WizardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WizardView.swift; sourceTree = "<group>"; };
		A1234567890123456789022 /* NewDocumentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewDocumentView.swift; sourceTree = "<group>"; };
		A1234567890123456789024 /* ExportView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExportView.swift; sourceTree = "<group>"; };
		A1234567890123456789026 /* ProjectTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProjectTypes.swift; sourceTree = "<group>"; };
		A1234567890123456789101 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		A1234567890123456789102 /* PRDGenerator.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = PRDGenerator.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1234567890123456789200 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		A1234567890123456789300 = {
			isa = PBXGroup;
			children = (
				A1234567890123456789301 /* PRDGenerator */,
				A1234567890123456789302 /* Products */,
			);
			sourceTree = "<group>";
		};
		A1234567890123456789301 /* PRDGenerator */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789002 /* PRDGeneratorApp.swift */,
				A1234567890123456789004 /* ContentView.swift */,
				A1234567890123456789018 /* DocumentDetailView.swift */,
				A1234567890123456789020 /* WizardView.swift */,
				A1234567890123456789022 /* NewDocumentView.swift */,
				A1234567890123456789024 /* ExportView.swift */,
				A1234567890123456789016 /* WizardViewModel.swift */,
				A1234567890123456789014 /* DataService.swift */,
				A1234567890123456789012 /* PersistenceController.swift */,
				A1234567890123456789026 /* ProjectTypes.swift */,
				A1234567890123456789010 /* PRDGenerator.xcdatamodeld */,
				A1234567890123456789006 /* Assets.xcassets */,
				A1234567890123456789101 /* Info.plist */,
				A1234567890123456789102 /* PRDGenerator.entitlements */,
				A1234567890123456789303 /* Preview Content */,
			);
			path = PRDGenerator;
			sourceTree = "<group>";
		};
		A1234567890123456789302 /* Products */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789100 /* PRDGenerator.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		A1234567890123456789303 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				A1234567890123456789008 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A1234567890123456789400 /* PRDGenerator */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = A1234567890123456789401 /* Build configuration list for PBXNativeTarget "PRDGenerator" */;
			buildPhases = (
				A1234567890123456789402 /* Sources */,
				A1234567890123456789200 /* Frameworks */,
				A1234567890123456789403 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PRDGenerator;
			productName = PRDGenerator;
			productReference = A1234567890123456789100 /* PRDGenerator.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		A1234567890123456789500 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					A1234567890123456789400 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = A1234567890123456789501 /* Build configuration list for PBXProject "PRDGenerator" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = A1234567890123456789300;
			productRefGroup = A1234567890123456789302 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				A1234567890123456789400 /* PRDGenerator */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		A1234567890123456789403 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789007 /* Preview Assets.xcassets in Resources */,
				A1234567890123456789005 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		A1234567890123456789402 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				A1234567890123456789001 /* PRDGeneratorApp.swift in Sources */,
				A1234567890123456789003 /* ContentView.swift in Sources */,
				A1234567890123456789017 /* DocumentDetailView.swift in Sources */,
				A1234567890123456789019 /* WizardView.swift in Sources */,
				A1234567890123456789021 /* NewDocumentView.swift in Sources */,
				A1234567890123456789023 /* ExportView.swift in Sources */,
				A1234567890123456789015 /* WizardViewModel.swift in Sources */,
				A1234567890123456789013 /* DataService.swift in Sources */,
				A1234567890123456789011 /* PersistenceController.swift in Sources */,
				A1234567890123456789025 /* ProjectTypes.swift in Sources */,
				A1234567890123456789009 /* PRDGenerator.xcdatamodeld in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		A1234567890123456789600 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		A1234567890123456789601 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		A1234567890123456789602 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PRDGenerator/PRDGenerator.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PRDGenerator/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PRDGenerator/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.prdgenerator.PRDGenerator;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		A1234567890123456789603 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PRDGenerator/PRDGenerator.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"PRDGenerator/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PRDGenerator/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.prdgenerator.PRDGenerator;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		A1234567890123456789501 /* Build configuration list for PBXProject "PRDGenerator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789600 /* Debug */,
				A1234567890123456789601 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		A1234567890123456789401 /* Build configuration list for PBXNativeTarget "PRDGenerator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				A1234567890123456789602 /* Debug */,
				A1234567890123456789603 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		A1234567890123456789010 /* PRDGenerator.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				A1234567890123456789011 /* PRDGenerator.xcdatamodel */,
			);
			currentVersion = A1234567890123456789011 /* PRDGenerator.xcdatamodel */;
			path = PRDGenerator.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */

	};
	rootObject = A1234567890123456789500 /* Project object */;
}