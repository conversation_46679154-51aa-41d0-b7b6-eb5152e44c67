// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		AA76B347A2E64E9D89D18AF2 /* PRDGeneratorApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 55E68389C3954E47BF256BB2 /* PRDGeneratorApp.swift */; };
		8D8AB34A3BAF49B787F14CD1 /* WizardViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 64F2E71AB98746B58E8EF0B5 /* WizardViewModel.swift */; };
		A12FC6DE7E4F411EAD0FB989 /* Question+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 546531BFB197436C9625830E /* Question+Extensions.swift */; };
		AED159B09E3F4B11A43A4048 /* Section+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 25A52178534545F2826D5670 /* Section+Extensions.swift */; };
		AC243745F6814CCCB30FAE07 /* PRDDocument+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = A37120DEB0D84FA5A752F78F /* PRDDocument+Extensions.swift */; };
		AF2E75FD62354C2FB22FD6CE /* Answer+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = D06456E1E5ED4CDB886FA525 /* Answer+Extensions.swift */; };
		AC28E389BCA145A783F552B7 /* PersistenceController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 05A0F7AB6DB84404B2B44D37 /* PersistenceController.swift */; };
		5E7E339AA3E44AB9AB5B705D /* LMSFeatureTemplate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1380866E990049EDA43493F1 /* LMSFeatureTemplate.swift */; };
		FF6DC976834B4598B47D7232 /* GeneralTemplate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 202DA392E3344AAB9E4099A7 /* GeneralTemplate.swift */; };
		B21F2B70A8554E3896DE2DFB /* AppDevelopmentTemplate.swift in Sources */ = {isa = PBXBuildFile; fileRef = F95C97C2FBB7462E8D62321E /* AppDevelopmentTemplate.swift */; };
		91FCA53255E44EEEB0492490 /* ProjectTypes.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7CE9D3FE22C141D0A38ED7F3 /* ProjectTypes.swift */; };
		DDA82702AA474F0E96977D06 /* LearningDevelopmentTemplate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8E75D6FC6D5345D08BBA4153 /* LearningDevelopmentTemplate.swift */; };
		7487ED5BDA5D4D1A8692B2A2 /* CoachingPlatformTemplate.swift in Sources */ = {isa = PBXBuildFile; fileRef = BE888FE5C2244A9BA484F406 /* CoachingPlatformTemplate.swift */; };
		94BAC529FD2444DDAD1C9897 /* TemplateBuilderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DCF0D33F592E4A4D893758C4 /* TemplateBuilderView.swift */; };
		E6B8A91AF1E644E3A50AC264 /* AddQuestionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 48439C6BD6A84B88874C2612 /* AddQuestionView.swift */; };
		42581A450FC94907B144BC06 /* WizardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4BC7319C41FB40F4AEF46E01 /* WizardView.swift */; };
		79B085475CE54E8ABFC51F84 /* DocumentLibraryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9C5EE099D4034C50B5C3EEEB /* DocumentLibraryView.swift */; };
		6DDC2DB7CE8349508A7EF55D /* DocumentDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 040942388F7F469D9DC112C1 /* DocumentDetailView.swift */; };
		268914D6CD4D4B70AB6697FD /* ExportView.swift in Sources */ = {isa = PBXBuildFile; fileRef = B97EC09332A244129B5301CB /* ExportView.swift */; };
		3B248541C376417B9815B3B7 /* SectionDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 4B97C93E8E834A2795CB3998 /* SectionDetailView.swift */; };
		0F4FFF6C9F5B4CCA85C054D6 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0FCB7E96F3264FD69F65B1D1 /* ContentView.swift */; };
		F1CC0E5846F74398AF114893 /* NewDocumentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 59A918F739FF4F57A20AC03B /* NewDocumentView.swift */; };
		C579176B2C1F4E5783AD9BF8 /* TemplateService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5CFE33401FE842B2B27E80C3 /* TemplateService.swift */; };
		3B30222AE4B649F988233560 /* DataService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 9D43592B4AEE4A17B2E246CB /* DataService.swift */; };
		52AD7DA6613C42C390EDB73A /* PDFExportService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6D73519CE6EE41B2AF55B782 /* PDFExportService.swift */; };
		9669F6464CC14D608CA0B5CA /* PRDGenerationService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 73C2D6D290034A56900BE3D2 /* PRDGenerationService.swift */; };
		7FC28B91CD2B4B3AA95F2AEC /* Assets.xcassets in Sources */ = {isa = PBXBuildFile; fileRef = E274C896020146F6A02BEF31 /* Assets.xcassets */; };
		37F7355902F4483C8EDAD033 /* Preview Assets.xcassets in Sources */ = {isa = PBXBuildFile; fileRef = 468EFE4D19224A0893B12415 /* Preview Assets.xcassets */; };
		8E19A4E39FA04A5E98F41183 /* PRDGenerator.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = 6E218733E3134D0BB3B459F0 /* PRDGenerator.xcdatamodeld */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		03FB67506634434890F8E3D5 /* PRDGenerator.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PRDGenerator.app; sourceTree = BUILT_PRODUCTS_DIR; };
		55E68389C3954E47BF256BB2 /* PRDGeneratorApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PRDGeneratorApp.swift; sourceTree = "<group>"; };
		64F2E71AB98746B58E8EF0B5 /* WizardViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WizardViewModel.swift; sourceTree = "<group>"; };
		546531BFB197436C9625830E /* Question+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Question+Extensions.swift; sourceTree = "<group>"; };
		25A52178534545F2826D5670 /* Section+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Section+Extensions.swift; sourceTree = "<group>"; };
		A37120DEB0D84FA5A752F78F /* PRDDocument+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PRDDocument+Extensions.swift; sourceTree = "<group>"; };
		D06456E1E5ED4CDB886FA525 /* Answer+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Answer+Extensions.swift; sourceTree = "<group>"; };
		05A0F7AB6DB84404B2B44D37 /* PersistenceController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PersistenceController.swift; sourceTree = "<group>"; };
		1380866E990049EDA43493F1 /* LMSFeatureTemplate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LMSFeatureTemplate.swift; sourceTree = "<group>"; };
		202DA392E3344AAB9E4099A7 /* GeneralTemplate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GeneralTemplate.swift; sourceTree = "<group>"; };
		F95C97C2FBB7462E8D62321E /* AppDevelopmentTemplate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDevelopmentTemplate.swift; sourceTree = "<group>"; };
		7CE9D3FE22C141D0A38ED7F3 /* ProjectTypes.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProjectTypes.swift; sourceTree = "<group>"; };
		8E75D6FC6D5345D08BBA4153 /* LearningDevelopmentTemplate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LearningDevelopmentTemplate.swift; sourceTree = "<group>"; };
		BE888FE5C2244A9BA484F406 /* CoachingPlatformTemplate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoachingPlatformTemplate.swift; sourceTree = "<group>"; };
		DCF0D33F592E4A4D893758C4 /* TemplateBuilderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TemplateBuilderView.swift; sourceTree = "<group>"; };
		48439C6BD6A84B88874C2612 /* AddQuestionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddQuestionView.swift; sourceTree = "<group>"; };
		4BC7319C41FB40F4AEF46E01 /* WizardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WizardView.swift; sourceTree = "<group>"; };
		9C5EE099D4034C50B5C3EEEB /* DocumentLibraryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentLibraryView.swift; sourceTree = "<group>"; };
		040942388F7F469D9DC112C1 /* DocumentDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DocumentDetailView.swift; sourceTree = "<group>"; };
		B97EC09332A244129B5301CB /* ExportView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExportView.swift; sourceTree = "<group>"; };
		4B97C93E8E834A2795CB3998 /* SectionDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SectionDetailView.swift; sourceTree = "<group>"; };
		0FCB7E96F3264FD69F65B1D1 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		59A918F739FF4F57A20AC03B /* NewDocumentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewDocumentView.swift; sourceTree = "<group>"; };
		5CFE33401FE842B2B27E80C3 /* TemplateService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TemplateService.swift; sourceTree = "<group>"; };
		9D43592B4AEE4A17B2E246CB /* DataService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataService.swift; sourceTree = "<group>"; };
		6D73519CE6EE41B2AF55B782 /* PDFExportService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PDFExportService.swift; sourceTree = "<group>"; };
		73C2D6D290034A56900BE3D2 /* PRDGenerationService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PRDGenerationService.swift; sourceTree = "<group>"; };
		E274C896020146F6A02BEF31 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		468EFE4D19224A0893B12415 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Preview Assets.xcassets; sourceTree = "<group>"; };
		6E218733E3134D0BB3B459F0 /* PRDGenerator.xcdatamodeld */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodeld; path = PRDGenerator.xcdatamodeld; sourceTree = "<group>"; };
		B0F3D1789D5E4747B153310F /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		33C79C9512C2480AA3098395 /* PRDGenerator.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = PRDGenerator.entitlements; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		A1C14EF741B84830A871285A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		5A5F8FF35ECD4ACCB0E8FB48 = {
			isa = PBXGroup;
			children = (
				7AC32EE7C057440690E1AA92 /* PRDGenerator */,
				5335724DC92041C6ACF3A452 /* Products */,
			);
			sourceTree = "<group>";
		};
		8093A886E80740258F61C3BD /* Products */ = {
			isa = PBXGroup;
			children = (
				03FB67506634434890F8E3D5 /* PRDGenerator.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		A251EFBDA19B4DDD87FFFAEE /* PRDGenerator */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = BD21312177F74B2EA1806447 /* Build configuration list for PBXNativeTarget "PRDGenerator" */;
			buildPhases = (
				F5D9893C60434AB9B270B80A /* Sources */,
				454959B6388F481ABBD8F42A /* Frameworks */,
				A6198B56F2C5414EA2E5CFC4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = PRDGenerator;
			productName = PRDGenerator;
			productReference = 03FB67506634434890F8E3D5 /* PRDGenerator.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		5092E113D5524F0BB168D023 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1500;
				TargetAttributes = {
					62E69F867741404A8746F8D6 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 7BDCD86DDD5F41F5B3A0F9EB /* Build configuration list for PBXProject "PRDGenerator" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C84F5A75E2B240EB94B774C9;
			productRefGroup = 7900505842204B45B8E173E5 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				01B09CC0773845669121FB86 /* PRDGenerator */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		73AEA72AF9C645E68B381B70 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		5C1DE3E65C284ED3B52BF1D7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AA76B347A2E64E9D89D18AF2 /* PRDGeneratorApp.swift in Sources */,
				8D8AB34A3BAF49B787F14CD1 /* WizardViewModel.swift in Sources */,
				A12FC6DE7E4F411EAD0FB989 /* Question+Extensions.swift in Sources */,
				AED159B09E3F4B11A43A4048 /* Section+Extensions.swift in Sources */,
				AC243745F6814CCCB30FAE07 /* PRDDocument+Extensions.swift in Sources */,
				AF2E75FD62354C2FB22FD6CE /* Answer+Extensions.swift in Sources */,
				AC28E389BCA145A783F552B7 /* PersistenceController.swift in Sources */,
				5E7E339AA3E44AB9AB5B705D /* LMSFeatureTemplate.swift in Sources */,
				FF6DC976834B4598B47D7232 /* GeneralTemplate.swift in Sources */,
				B21F2B70A8554E3896DE2DFB /* AppDevelopmentTemplate.swift in Sources */,
				91FCA53255E44EEEB0492490 /* ProjectTypes.swift in Sources */,
				DDA82702AA474F0E96977D06 /* LearningDevelopmentTemplate.swift in Sources */,
				7487ED5BDA5D4D1A8692B2A2 /* CoachingPlatformTemplate.swift in Sources */,
				94BAC529FD2444DDAD1C9897 /* TemplateBuilderView.swift in Sources */,
				E6B8A91AF1E644E3A50AC264 /* AddQuestionView.swift in Sources */,
				42581A450FC94907B144BC06 /* WizardView.swift in Sources */,
				79B085475CE54E8ABFC51F84 /* DocumentLibraryView.swift in Sources */,
				6DDC2DB7CE8349508A7EF55D /* DocumentDetailView.swift in Sources */,
				268914D6CD4D4B70AB6697FD /* ExportView.swift in Sources */,
				3B248541C376417B9815B3B7 /* SectionDetailView.swift in Sources */,
				0F4FFF6C9F5B4CCA85C054D6 /* ContentView.swift in Sources */,
				F1CC0E5846F74398AF114893 /* NewDocumentView.swift in Sources */,
				C579176B2C1F4E5783AD9BF8 /* TemplateService.swift in Sources */,
				3B30222AE4B649F988233560 /* DataService.swift in Sources */,
				52AD7DA6613C42C390EDB73A /* PDFExportService.swift in Sources */,
				9669F6464CC14D608CA0B5CA /* PRDGenerationService.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		FCB6ED727B084CFFB69A6843 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $$(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		67E1D90866B24225A59EBF36 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		25841DAA5E144351B88C0B0E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PRDGenerator/PRDGenerator.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "PRDGenerator/Preview\ Content";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PRDGenerator/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.prdgenerator.PRDGenerator;
				PRODUCT_NAME = "$$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		1249BBAD2B054079A2548D59 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PRDGenerator/PRDGenerator.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "PRDGenerator/Preview\ Content";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = PRDGenerator/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.prdgenerator.PRDGenerator;
				PRODUCT_NAME = "$$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1E45E1E06D944ED8809631A3 /* Build configuration list for PBXProject "PRDGenerator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				91ED114E1ADC452C90DF2C11 /* Debug */,
				9EE54825AEBF4A6A9F199012 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		11DA4C21E56A4FA1951973C2 /* Build configuration list for PBXNativeTarget "PRDGenerator" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				D0120D4D0724423B8DF72259 /* Debug */,
				2EBF9BE41FAE40EBB13A0023 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		6E218733E3134D0BB3B459F0 /* PRDGenerator.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				B04C752E7CD94FC6B204D711 /* PRDGenerator.xcdatamodel */,
			);
			currentVersion = E304FAC452A44CD99ADEFF9D /* PRDGenerator.xcdatamodel */;
			path = PRDGenerator.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */

	};
	rootObject = D2A24E210B89463CB34BE87F /* Project object */;
}
