client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PRDGenerator-arm64-apple-macosx15.0-debug.module": ["<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources"]
    description: "Write auxiliary file /Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets":
    tool: copy-tool
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Resources/Assets.xcassets/"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets/"]
    description: "Copying /Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Resources/Assets.xcassets"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld":
    tool: copy-tool
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDGenerator.xcdatamodeld/"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld/"]
    description: "Copying /Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDGenerator.xcdatamodeld"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<PRDGenerator-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule"]
    outputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]

  "C.PRDGenerator-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<PRDGenerator-arm64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule"]
    description: "Compiling Swift Module 'PRDGenerator' (28 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","PRDGenerator","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule","-output-file-map","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/output-file-map.json","-incremental","@/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources","-I","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx12.0","-enable-batch-mode","-index-store-path","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-swift-version","5","-Xfrontend","-experimental-lazy-typecheck","-Xfrontend","-experimental-skip-all-function-bodies","-Xfrontend","-experimental-allow-module-with-compiler-errors","-Xfrontend","-empty-abi-descriptor","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","prdgenerator_working"]

  "PRDGenerator-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld/","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets/"]
    outputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.module-resources>"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/","/Users/<USER>/m.o.a/PRDGenerator-Working/Package.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

