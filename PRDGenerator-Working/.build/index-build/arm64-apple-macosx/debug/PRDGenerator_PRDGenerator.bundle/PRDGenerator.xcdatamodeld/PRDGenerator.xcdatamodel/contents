<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="22222" systemVersion="23A344" minimumToolsVersion="Automatic" sourceLanguage="Swift" userDefinedModelVersionIdentifier="">
    <entity name="Answer" representedClassName="Answer" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="value" optional="YES" attributeType="String"/>
        <relationship name="question" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Question" inverseName="answer" inverseEntity="Question"/>
    </entity>
    <entity name="PRDDocument" representedClassName="PRDDocument" syncable="YES" codeGenerationType="class">
        <attribute name="createdDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isCompleted" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="modifiedDate" optional="YES" attributeType="Date" usesScalarValueType="NO"/>
        <attribute name="projectType" optional="YES" attributeType="String"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <relationship name="sections" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Section" inverseName="document" inverseEntity="Section"/>
    </entity>
    <entity name="Question" representedClassName="Question" syncable="YES" codeGenerationType="class">
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="isRequired" optional="YES" attributeType="Boolean" usesScalarValueType="YES"/>
        <attribute name="options" optional="YES" attributeType="String"/>
        <attribute name="order" optional="YES" attributeType="Integer 16" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="questionType" optional="YES" attributeType="String"/>
        <attribute name="text" optional="YES" attributeType="String"/>
        <relationship name="answer" optional="YES" maxCount="1" deletionRule="Cascade" destinationEntity="Answer" inverseName="question" inverseEntity="Answer"/>
        <relationship name="section" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="Section" inverseName="questions" inverseEntity="Section"/>
    </entity>
    <entity name="Section" representedClassName="Section" syncable="YES" codeGenerationType="class">
        <attribute name="content" optional="YES" attributeType="String"/>
        <attribute name="id" optional="YES" attributeType="UUID" usesScalarValueType="NO"/>
        <attribute name="order" optional="YES" attributeType="Integer 16" defaultValue="0" usesScalarValueType="YES"/>
        <attribute name="sectionType" optional="YES" attributeType="String"/>
        <attribute name="title" optional="YES" attributeType="String"/>
        <relationship name="document" optional="YES" maxCount="1" deletionRule="Nullify" destinationEntity="PRDDocument" inverseName="sections" inverseEntity="PRDDocument"/>
        <relationship name="questions" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Question" inverseName="section" inverseEntity="Question"/>
    </entity>
</model>