{"builtTestProducts": [], "copyCommands": {"/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Resources/Assets.xcassets"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets"}]}, "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDGenerator.xcdatamodeld"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.PRDGenerator-arm64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources", "importPath": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Package.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<PRDGenerator-arm64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources"}], "isLibrary": false, "moduleName": "PRDGenerator", "moduleOutputPath": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule", "objects": ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/Answer+Extensions.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/PRDDocument+Extensions.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/PersistenceController.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/Question+Extensions.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/Section+Extensions.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/Package.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DataService.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/ExportService.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/PDFExportService.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/PRDGenerationService.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateService.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/AppDevelopmentTemplate.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/CoachingPlatformTemplate.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/GeneralTemplate.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/LMSFeatureTemplate.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/LearningDevelopmentTemplate.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/ProjectTypes.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/WizardViewModel.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/AddQuestionView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/ContentView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentDetailView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentLibraryView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/ExportView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/NewDocumentView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/SectionDetailView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateBuilderView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/WizardView.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/main.swift.o", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "PRDGenerator_main", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "prdgenerator_working"], "outputFileMapPath": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule"}], "prepareForIndexing": true, "sources": ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Package.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"PRDGenerator": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "PRDGenerator", "-package-name", "prdgenerator_working", "-incremental", "-c", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Package.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/Modules", "-target", "arm64-apple-macosx12.0", "-enable-batch-mode", "-<PERSON><PERSON>", "-enable-testing", "-j10", "-DSWIFT_PACKAGE", "-DDEBUG", "-module-cache-path", "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "PRDGenerator_main", "-swift-version", "5", "-Xfrontend", "-experimental-lazy-typecheck", "-Xfrontend", "-experimental-skip-all-function-bodies", "-Xfrontend", "-experimental-allow-module-with-compiler-errors", "-Xfrontend", "-empty-abi-descriptor", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "prdgenerator_working", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"PRDGenerator": []}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Package.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift"}, {"kind": "file", "name": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/PRDGenerator.build/sources"}, "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/index-build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}