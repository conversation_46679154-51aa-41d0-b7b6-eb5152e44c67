client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PRDGenerator-arm64-apple-macosx15.0-debug.exe": ["<PRDGenerator-arm64-apple-macosx15.0-debug.exe>"]
  "PRDGenerator-arm64-apple-macosx15.0-debug.module": ["<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]
  "PackageStructure": ["<PackageStructure>"]
  "main": ["<PRDGenerator-arm64-apple-macosx15.0-debug.exe>","<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]
  "test": ["<PRDGenerator-arm64-apple-macosx15.0-debug.exe>","<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator":
    is-mutated: true
commands:
  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator-entitlement.plist":
    tool: write-auxiliary-file
    inputs: ["<entitlement-plist>","<com.apple.security.get-task-allow>"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator-entitlement.plist"]
    description: "Write auxiliary file /Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator-entitlement.plist"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/sources"]
    description: "Write auxiliary file /Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/sources"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AddQuestionView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Answer+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AppDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/CoachingPlatformTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ContentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DataService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentLibraryView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/GeneralTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LMSFeatureTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LearningDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/NewDocumentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PDFExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDDocument+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDGenerationService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PersistenceController.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ProjectTypes.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Question+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Section+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/SectionDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateBuilderView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardViewModel.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/main.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/resource_bundle_accessor.swift.o"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.product/Objects.LinkFileList"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets":
    tool: copy-tool
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Resources/Assets.xcassets/"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets/"]
    description: "Copying /Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Resources/Assets.xcassets"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld":
    tool: copy-tool
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDGenerator.xcdatamodeld/"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld/"]
    description: "Copying /Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDGenerator.xcdatamodeld"

  "/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"

  "<PRDGenerator-arm64-apple-macosx15.0-debug.exe>":
    tool: phony
    inputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    outputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.exe>"]

  "<PRDGenerator-arm64-apple-macosx15.0-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Answer+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDDocument+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PersistenceController.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Question+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Section+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DataService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PDFExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDGenerationService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AppDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/CoachingPlatformTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/GeneralTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LMSFeatureTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LearningDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ProjectTypes.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardViewModel.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AddQuestionView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ContentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentLibraryView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/NewDocumentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/SectionDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateBuilderView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/main.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/resource_bundle_accessor.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule"]
    outputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.module>"]

  "C.PRDGenerator-arm64-apple-macosx15.0-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AddQuestionView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Answer+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AppDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/CoachingPlatformTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ContentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DataService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentLibraryView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/GeneralTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LMSFeatureTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LearningDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/NewDocumentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PDFExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDDocument+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDGenerationService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PersistenceController.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ProjectTypes.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Question+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Section+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/SectionDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateBuilderView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardViewModel.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/main.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/resource_bundle_accessor.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator"]
    description: "Linking ./.build/arm64-apple-macosx/debug/PRDGenerator"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug","-o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator","-module-name","PRDGenerator","-Xlinker","-no_warn_duplicate_libraries","-emit-executable","-Xlinker","-alias","-Xlinker","_PRDGenerator_main","-Xlinker","_main","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.product/Objects.LinkFileList","-target","arm64-apple-macosx12.0","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g"]

  "C.PRDGenerator-arm64-apple-macosx15.0-debug.exe-entitlements":
    tool: shell
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator-entitlement.plist"]
    outputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.exe-CodeSigning>"]
    description: "Applying debug entitlements to ./.build/arm64-apple-macosx/debug/PRDGenerator"
    args: ["codesign","--force","--sign","-","--entitlements","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator-entitlement.plist","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator"]

  "C.PRDGenerator-arm64-apple-macosx15.0-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Answer+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PRDDocument+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/PersistenceController.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Question+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Models/Section+Extensions.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/DataService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/ExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PDFExportService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/PRDGenerationService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Services/TemplateService.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/AppDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/CoachingPlatformTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/GeneralTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LMSFeatureTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/LearningDevelopmentTemplate.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Templates/ProjectTypes.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/ViewModels/WizardViewModel.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/AddQuestionView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ContentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/DocumentLibraryView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/ExportView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/NewDocumentView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/SectionDetailView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/TemplateBuilderView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/Views/WizardView.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/main.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt","<PRDGenerator-arm64-apple-macosx15.0-debug.module-resources>","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/sources"]
    outputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Answer+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDDocument+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PersistenceController.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Question+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/Section+Extensions.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DataService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PDFExportService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/PRDGenerationService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateService.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AppDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/CoachingPlatformTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/GeneralTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LMSFeatureTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/LearningDevelopmentTemplate.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ProjectTypes.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardViewModel.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/AddQuestionView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ContentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/DocumentLibraryView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/ExportView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/NewDocumentView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/SectionDetailView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/TemplateBuilderView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/WizardView.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/main.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/resource_bundle_accessor.swift.o","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule"]
    description: "Compiling Swift Module 'PRDGenerator' (28 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","PRDGenerator","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/Modules/PRDGenerator.swiftmodule","-output-file-map","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/output-file-map.json","-incremental","-c","@/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator.build/sources","-I","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/Modules","-target","arm64-apple-macosx12.0","-enable-batch-mode","-index-store-path","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/index/store","-Onone","-enable-testing","-j10","-DSWIFT_PACKAGE","-DDEBUG","-module-cache-path","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/ModuleCache","-parseable-output","-Xfrontend","-entry-point-function-name","-Xfrontend","PRDGenerator_main","-color-diagnostics","-swift-version","5","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","prdgenerator_working"]

  "PRDGenerator-arm64-apple-macosx15.0-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/PRDGenerator.xcdatamodeld/","/Users/<USER>/m.o.a/PRDGenerator-Working/.build/arm64-apple-macosx/debug/PRDGenerator_PRDGenerator.bundle/Assets.xcassets/"]
    outputs: ["<PRDGenerator-arm64-apple-macosx15.0-debug.module-resources>"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/m.o.a/PRDGenerator-Working/Sources/PRDGenerator/","/Users/<USER>/m.o.a/PRDGenerator-Working/Package.swift","/Users/<USER>/m.o.a/PRDGenerator-Working/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

