import Foundation

// MARK: - Project Types and Templates
enum ProjectType: String, CaseIterable, Identifiable {
    case learningDevelopment = "Learning & Development"
    case coachingPlatform = "Coaching Platform"
    case lmsFeature = "LMS Feature"
    case appDevelopment = "App Development"
    case general = "General"
    
    var id: String { self.rawValue }
    
    var description: String {
        switch self {
        case .learningDevelopment:
            return "Educational content, training programs, and skill development platforms"
        case .coachingPlatform:
            return "Personal coaching, mentorship, and guidance applications"
        case .lmsFeature:
            return "Learning Management System features and modules"
        case .appDevelopment:
            return "Mobile and web application development projects"
        case .general:
            return "General purpose product requirements document"
        }
    }
    
    var icon: String {
        switch self {
        case .learningDevelopment:
            return "graduationcap"
        case .coachingPlatform:
            return "person.2"
        case .lmsFeature:
            return "book"
        case .appDevelopment:
            return "app"
        case .general:
            return "doc.text"
        }
    }
    
    var sections: [PRDSection] {
        switch self {
        case .learningDevelopment:
            return LearningDevelopmentTemplate.sections
        case .coachingPlatform:
            return CoachingPlatformTemplate.sections
        case .lmsFeature:
            return LMSFeatureTemplate.sections
        case .appDevelopment:
            return AppDevelopmentTemplate.sections
        case .general:
            return GeneralTemplate.sections
        }
    }
}

// MARK: - PRD Section Structure
struct PRDSection: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let sectionType: String
    let order: Int
    let questions: [PRDQuestion]
    let isRequired: Bool
}

struct PRDQuestion: Identifiable {
    let id = UUID()
    let text: String
    let questionType: QuestionType
    let options: [String]?
    let isRequired: Bool
    let order: Int
    let placeholder: String?
    let helpText: String?
}

enum QuestionType: String, CaseIterable {
    case textInput = "textInput"
    case richText = "richText"
    case multipleChoice = "multipleChoice"
    case singleSelect = "singleSelect"
    case dateInput = "dateInput"
    case numberInput = "numberInput"
    case urlInput = "urlInput"
    case emailInput = "emailInput"
}