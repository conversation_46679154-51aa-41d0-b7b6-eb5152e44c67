import Foundation

struct CoachingPlatformTemplate {
    static let sections: [PRDSection] = [
        // Platform Overview
        PRDSection(
            title: "Platform Overview",
            description: "High-level description of the coaching platform and methodology",
            sectionType: "overview",
            order: 1,
            questions: [
                PRDQuestion(
                    text: "What is the name of your coaching platform?",
                    questionType: .textInput,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "e.g., MindfulCoach Pro",
                    helpText: "Provide a clear, memorable name for your coaching platform"
                ),
                PRDQuestion(
                    text: "What coaching methodology or approach will the platform use?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe your coaching philosophy and methods",
                    helpText: "Include frameworks like GROW, Solution-Focused, CBT, etc."
                ),
                PRDQuestion(
                    text: "What are the primary coaching niches or specializations?",
                    questionType: .multipleChoice,
                    options: ["Life Coaching", "Executive Coaching", "Career Coaching", "Health & Wellness", "Relationship Coaching", "Business Coaching", "Performance Coaching", "Other"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select all applicable coaching areas"
                ),
                PRDQuestion(
                    text: "What types of coaching sessions will be supported?",
                    questionType: .multipleChoice,
                    options: ["1-on-1 Sessions", "Group Coaching", "Workshops", "Masterclasses", "Self-Paced Programs", "Peer Coaching"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select all session types the platform will offer"
                )
            ],
            isRequired: true
        ),
        
        // User Roles & Permissions
        PRDSection(
            title: "User Roles & Permissions",
            description: "Define different user types and their capabilities within the platform",
            sectionType: "user_roles",
            order: 2,
            questions: [
                PRDQuestion(
                    text: "What capabilities should coaches have on the platform?",
                    questionType: .multipleChoice,
                    options: ["Create coaching programs", "Schedule sessions", "Manage client profiles", "Track progress", "Send messages", "Create assessments", "Generate reports", "Set pricing", "Manage payments"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select all features coaches should be able to access"
                ),
                PRDQuestion(
                    text: "What features should clients/coachees have access to?",
                    questionType: .multipleChoice,
                    options: ["Book sessions", "View progress", "Complete assessments", "Access resources", "Message coach", "Join group sessions", "Track goals", "Make payments", "Leave feedback"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select all features clients should be able to use"
                ),
                PRDQuestion(
                    text: "What administrative and management tools are needed?",
                    questionType: .multipleChoice,
                    options: ["User management", "Platform analytics", "Financial reporting", "Content moderation", "Support ticketing", "Platform configuration", "Coach verification", "Quality assurance"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select administrative features for platform management"
                )
            ],
            isRequired: true
        ),
        
        // Core Features
        PRDSection(
            title: "Core Platform Features",
            description: "Essential features and functionality for the coaching platform",
            sectionType: "features",
            order: 3,
            questions: [
                PRDQuestion(
                    text: "What scheduling and calendar features are required?",
                    questionType: .multipleChoice,
                    options: ["Calendar integration", "Automated scheduling", "Time zone handling", "Recurring sessions", "Cancellation policies", "Waitlist management", "Reminder notifications"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select all scheduling features needed"
                ),
                PRDQuestion(
                    text: "What communication tools should be included?",
                    questionType: .multipleChoice,
                    options: ["Video conferencing", "Voice calls", "Text messaging", "File sharing", "Session notes", "Whiteboard", "Screen sharing", "Recording capability"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select communication features for coach-client interaction"
                ),
                PRDQuestion(
                    text: "What progress tracking and assessment features are needed?",
                    questionType: .multipleChoice,
                    options: ["Goal setting", "Progress metrics", "Custom assessments", "Mood tracking", "Habit tracking", "Journal entries", "Photo progress", "Data visualization"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select features for tracking client progress and outcomes"
                ),
                PRDQuestion(
                    text: "What payment and billing features are required?",
                    questionType: .multipleChoice,
                    options: ["One-time payments", "Subscription billing", "Package deals", "Payment plans", "Refund processing", "Invoice generation", "Tax handling", "Multiple currencies"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select payment processing features needed"
                )
            ],
            isRequired: true
        ),
        
        // Technical Requirements
        PRDSection(
            title: "Technical Requirements",
            description: "Platform technical specifications and integration needs",
            sectionType: "technical",
            order: 4,
            questions: [
                PRDQuestion(
                    text: "What platforms should the coaching application support?",
                    questionType: .multipleChoice,
                    options: ["Web Browser", "iOS App", "Android App", "Desktop App (Mac)", "Desktop App (Windows)"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select all platforms where the coaching app should be available"
                ),
                PRDQuestion(
                    text: "What third-party integrations are needed?",
                    questionType: .multipleChoice,
                    options: ["Google Calendar", "Outlook Calendar", "Zoom", "Stripe", "PayPal", "Mailchimp", "Slack", "Zapier", "Google Analytics"],
                    isRequired: false,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select external services to integrate with"
                ),
                PRDQuestion(
                    text: "What security and compliance requirements must be met?",
                    questionType: .multipleChoice,
                    options: ["GDPR Compliance", "HIPAA Compliance", "SOC 2", "End-to-end encryption", "Two-factor authentication", "Data backup", "Audit logging"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select security and compliance standards to implement"
                ),
                PRDQuestion(
                    text: "What are the expected user capacity and performance requirements?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe expected number of users, concurrent sessions, response times, etc.",
                    helpText: "Include details about scalability, performance benchmarks, and growth projections"
                )
            ],
            isRequired: true
        ),
        
        // Success Metrics
        PRDSection(
            title: "Success Metrics & KPIs",
            description: "Key performance indicators and success measurements for the coaching platform",
            sectionType: "metrics",
            order: 5,
            questions: [
                PRDQuestion(
                    text: "What are the primary success metrics for coaches?",
                    questionType: .multipleChoice,
                    options: ["Client retention rate", "Session completion rate", "Revenue per coach", "Client satisfaction scores", "Goal achievement rate", "Referral rate"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select metrics to measure coach success on the platform"
                ),
                PRDQuestion(
                    text: "What are the key metrics for client success and engagement?",
                    questionType: .multipleChoice,
                    options: ["Goal completion rate", "Session attendance", "Platform usage frequency", "Progress milestone achievements", "Satisfaction ratings", "Program completion rate"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select metrics to measure client outcomes and engagement"
                ),
                PRDQuestion(
                    text: "What business metrics will measure platform success?",
                    questionType: .multipleChoice,
                    options: ["Monthly recurring revenue", "User acquisition cost", "Lifetime value", "Churn rate", "Platform utilization", "Support ticket volume"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select business metrics for overall platform performance"
                )
            ],
            isRequired: true
        )
    ]
}