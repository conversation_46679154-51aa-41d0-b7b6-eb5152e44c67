import Foundation

struct GeneralTemplate {
    static let sections: [PRDSection] = [
        // Project Overview
        PRDSection(
            title: "Project Overview",
            description: "High-level description of the product or project",
            sectionType: "overview",
            order: 1,
            questions: [
                PRDQuestion(
                    text: "What is the name of your product or project?",
                    questionType: .textInput,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "e.g., Customer Portal, Analytics Dashboard",
                    helpText: "Provide a clear, descriptive name for your product"
                ),
                PRDQuestion(
                    text: "What is the primary purpose of this product?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe what this product does and why it's needed",
                    helpText: "Focus on the core value proposition and main objectives"
                ),
                PRDQuestion(
                    text: "Who are the target users for this product?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe your target audience and user personas",
                    helpText: "Include demographics, roles, technical proficiency, and use cases"
                ),
                PRDQuestion(
                    text: "What business problem does this product solve?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe the business need or problem being addressed",
                    helpText: "Explain the current pain points and how this product will address them"
                )
            ],
            isRequired: true
        ),
        
        // Business Requirements
        PRDSection(
            title: "Business Requirements",
            description: "Business objectives and requirements for the product",
            sectionType: "business",
            order: 2,
            questions: [
                PRDQuestion(
                    text: "What are the primary business objectives for this product?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "List the key business goals this product should achieve",
                    helpText: "Include revenue goals, efficiency improvements, user satisfaction targets, etc."
                ),
                PRDQuestion(
                    text: "What are the success criteria and key performance indicators (KPIs)?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Define measurable success metrics",
                    helpText: "Include specific, quantifiable metrics that will indicate success"
                ),
                PRDQuestion(
                    text: "What is the expected timeline for this project?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe project phases, milestones, and delivery dates",
                    helpText: "Include development phases, testing periods, and launch timeline"
                ),
                PRDQuestion(
                    text: "What is the budget or resource allocation for this project?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 4,
                    placeholder: "Describe budget constraints and resource requirements",
                    helpText: "Include development costs, ongoing maintenance, and resource needs"
                )
            ],
            isRequired: true
        ),
        
        // Functional Requirements
        PRDSection(
            title: "Functional Requirements",
            description: "Detailed functional specifications and features",
            sectionType: "functional",
            order: 3,
            questions: [
                PRDQuestion(
                    text: "What are the core features and functionality required?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "List the essential features for the minimum viable product",
                    helpText: "Focus on must-have features that deliver core value"
                ),
                PRDQuestion(
                    text: "What user roles and permissions are needed?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe different user types and their access levels",
                    helpText: "Include admin, regular users, guests, and any specialized roles"
                ),
                PRDQuestion(
                    text: "What data needs to be captured, stored, and managed?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe data requirements and data flow",
                    helpText: "Include data types, sources, storage requirements, and data relationships"
                ),
                PRDQuestion(
                    text: "What business rules and validation logic are required?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe rules, constraints, and validation requirements",
                    helpText: "Include data validation, business logic, and system constraints"
                )
            ],
            isRequired: true
        ),
        
        // Technical Requirements
        PRDSection(
            title: "Technical Requirements",
            description: "Technical specifications and constraints",
            sectionType: "technical",
            order: 4,
            questions: [
                PRDQuestion(
                    text: "What platforms and technologies should be used?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "Specify preferred technologies, frameworks, and platforms",
                    helpText: "Include programming languages, databases, cloud platforms, etc."
                ),
                PRDQuestion(
                    text: "What are the performance requirements?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Specify response times, throughput, scalability needs",
                    helpText: "Include specific metrics for performance benchmarks"
                ),
                PRDQuestion(
                    text: "What integrations with existing systems are required?",
                    questionType: .richText,
                    options: nil,
                    isRequired: false,
                    order: 3,
                    placeholder: "List existing systems, APIs, or services to integrate with",
                    helpText: "Include internal systems, third-party services, and data sources"
                ),
                PRDQuestion(
                    text: "What security and compliance requirements must be met?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe security standards, compliance needs, and data protection",
                    helpText: "Include authentication, authorization, encryption, and regulatory compliance"
                )
            ],
            isRequired: true
        ),
        
        // User Experience
        PRDSection(
            title: "User Experience Requirements",
            description: "User interface and experience specifications",
            sectionType: "ux",
            order: 5,
            questions: [
                PRDQuestion(
                    text: "What are the key user workflows and user journeys?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "Describe the main user paths through the product",
                    helpText: "Include onboarding, core feature usage, and task completion flows"
                ),
                PRDQuestion(
                    text: "What are the user interface requirements?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe UI components, layout, and design requirements",
                    helpText: "Include navigation, forms, dashboards, and visual design preferences"
                ),
                PRDQuestion(
                    text: "What accessibility requirements need to be met?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe accessibility standards and inclusive design needs",
                    helpText: "Include WCAG compliance, screen reader support, keyboard navigation, etc."
                ),
                PRDQuestion(
                    text: "What devices and browsers need to be supported?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Specify supported devices, screen sizes, and browser requirements",
                    helpText: "Include mobile, tablet, desktop support and browser compatibility"
                )
            ],
            isRequired: true
        ),
        
        // Testing & Quality Assurance
        PRDSection(
            title: "Testing & Quality Assurance",
            description: "Testing requirements and quality criteria",
            sectionType: "testing",
            order: 6,
            questions: [
                PRDQuestion(
                    text: "What are the acceptance criteria for this product?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "Define specific, measurable criteria for product acceptance",
                    helpText: "Use clear, testable statements that define when the product is complete"
                ),
                PRDQuestion(
                    text: "What testing strategies and types of testing are required?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "Describe testing approach, including unit, integration, and user testing",
                    helpText: "Include automated testing, manual testing, and user acceptance testing"
                ),
                PRDQuestion(
                    text: "What are the key scenarios that must be tested?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "List critical user workflows and edge cases to test",
                    helpText: "Include happy path scenarios, error conditions, and boundary cases"
                )
            ],
            isRequired: true
        )
    ]
}