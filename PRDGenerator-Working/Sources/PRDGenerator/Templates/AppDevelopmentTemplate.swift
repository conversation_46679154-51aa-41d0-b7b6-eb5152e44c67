import Foundation

struct AppDevelopmentTemplate {
    static let sections: [PRDSection] = [
        // App Overview
        PRDSection(
            title: "App Overview",
            description: "High-level description of the mobile or web application",
            sectionType: "overview",
            order: 1,
            questions: [
                PRDQuestion(
                    text: "What is the name of your app?",
                    questionType: .textInput,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "e.g., FitTracker Pro, StudyBuddy",
                    helpText: "Provide a clear, memorable name for your application"
                ),
                PRDQuestion(
                    text: "What type of application are you building?",
                    questionType: .singleSelect,
                    options: ["Native iOS App", "Native Android App", "Cross-platform Mobile App", "Web Application", "Progressive Web App (PWA)", "Desktop Application"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select the primary platform for your application"
                ),
                PRDQuestion(
                    text: "What is the primary purpose of your app?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe what your app does and why users would want it",
                    helpText: "Focus on the core value proposition and main use case"
                ),
                PRDQuestion(
                    text: "Who is your target audience?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe your ideal users, their demographics, and characteristics",
                    helpText: "Include age groups, technical proficiency, use cases, and user personas"
                )
            ],
            isRequired: true
        ),
        
        // Market Analysis
        PRDSection(
            title: "Market Analysis",
            description: "Market research and competitive analysis for the application",
            sectionType: "market",
            order: 2,
            questions: [
                PRDQuestion(
                    text: "What problem does your app solve that existing solutions don't address well?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "Describe the gap in the market your app fills",
                    helpText: "Focus on unique value proposition and competitive advantages"
                ),
                PRDQuestion(
                    text: "Who are your main competitors?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 2,
                    placeholder: "List direct and indirect competitors",
                    helpText: "Include both direct competitors and alternative solutions users might choose"
                ),
                PRDQuestion(
                    text: "What is your app's unique selling proposition (USP)?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "What makes your app different and better than alternatives?",
                    helpText: "Focus on features, experience, or approach that sets you apart"
                ),
                PRDQuestion(
                    text: "What is your monetization strategy?",
                    questionType: .multipleChoice,
                    options: ["Free with Ads", "Freemium Model", "One-time Purchase", "Subscription", "In-app Purchases", "Enterprise Licensing", "Commission/Transaction Fees", "No Monetization"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select all applicable revenue models for your app"
                )
            ],
            isRequired: true
        ),
        
        // Core Features
        PRDSection(
            title: "Core Features & Functionality",
            description: "Essential features and functionality for the application",
            sectionType: "features",
            order: 3,
            questions: [
                PRDQuestion(
                    text: "What are the core features that define your app's MVP (Minimum Viable Product)?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "List the essential features needed for the first version",
                    helpText: "Focus on features absolutely necessary for the app to provide value"
                ),
                PRDQuestion(
                    text: "What user authentication and account features are needed?",
                    questionType: .multipleChoice,
                    options: ["Email/Password Login", "Social Media Login", "Phone Number Verification", "Biometric Authentication", "Guest Mode", "Account Recovery", "Profile Management", "Privacy Settings"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select authentication and account management features"
                ),
                PRDQuestion(
                    text: "What data management features are required?",
                    questionType: .multipleChoice,
                    options: ["Data Sync Across Devices", "Offline Mode", "Data Export", "Data Import", "Backup & Restore", "Data Sharing", "Search Functionality", "Data Filtering"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select data handling and management capabilities"
                ),
                PRDQuestion(
                    text: "What communication and social features are needed?",
                    questionType: .multipleChoice,
                    options: ["In-app Messaging", "Push Notifications", "Email Notifications", "Social Sharing", "Comments/Reviews", "User Profiles", "Friend/Follow System", "Community Features"],
                    isRequired: false,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select communication and social interaction features"
                )
            ],
            isRequired: true
        ),
        
        // Technical Requirements
        PRDSection(
            title: "Technical Requirements",
            description: "Technical specifications and platform requirements",
            sectionType: "technical",
            order: 4,
            questions: [
                PRDQuestion(
                    text: "What platforms and operating systems need to be supported?",
                    questionType: .multipleChoice,
                    options: ["iOS 14+", "iOS 15+", "iOS 16+", "Android 8+", "Android 10+", "Android 12+", "Web Browsers", "macOS", "Windows", "Linux"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select all platforms and minimum OS versions to support"
                ),
                PRDQuestion(
                    text: "What third-party services and APIs will be integrated?",
                    questionType: .multipleChoice,
                    options: ["Payment Processing", "Analytics", "Crash Reporting", "Push Notifications", "Maps/Location", "Social Media APIs", "Cloud Storage", "Authentication Services", "Email Services"],
                    isRequired: false,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select external services your app will integrate with"
                ),
                PRDQuestion(
                    text: "What are the performance requirements?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Specify app launch time, response times, memory usage, etc.",
                    helpText: "Include specific metrics for performance benchmarks"
                ),
                PRDQuestion(
                    text: "What security requirements must be implemented?",
                    questionType: .multipleChoice,
                    options: ["Data Encryption", "Secure API Communication", "Biometric Security", "Two-Factor Authentication", "Secure Storage", "Privacy Compliance", "Penetration Testing"],
                    isRequired: true,
                    order: 4,
                    placeholder: nil,
                    helpText: "Select security measures to implement"
                )
            ],
            isRequired: true
        ),
        
        // User Experience Design
        PRDSection(
            title: "User Experience & Design",
            description: "UX/UI design requirements and user experience goals",
            sectionType: "ux_design",
            order: 5,
            questions: [
                PRDQuestion(
                    text: "What is the desired look and feel for your app?",
                    questionType: .multipleChoice,
                    options: ["Modern/Minimalist", "Colorful/Vibrant", "Professional/Corporate", "Playful/Fun", "Dark Theme", "Light Theme", "Material Design", "iOS Human Interface"],
                    isRequired: true,
                    order: 1,
                    placeholder: nil,
                    helpText: "Select design styles and themes for your app"
                ),
                PRDQuestion(
                    text: "What accessibility features are required?",
                    questionType: .multipleChoice,
                    options: ["VoiceOver/TalkBack Support", "Large Text Support", "High Contrast Mode", "Voice Control", "Switch Control", "Reduced Motion", "Color Blind Support"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select accessibility features to implement"
                ),
                PRDQuestion(
                    text: "What are the key user flows that need to be optimized?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 3,
                    placeholder: "Describe the most important user journeys through your app",
                    helpText: "Focus on onboarding, core feature usage, and conversion flows"
                ),
                PRDQuestion(
                    text: "What onboarding experience do you want for new users?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Describe how new users will learn to use your app",
                    helpText: "Include tutorials, walkthroughs, or progressive disclosure strategies"
                )
            ],
            isRequired: true
        ),
        
        // Launch Strategy
        PRDSection(
            title: "Launch Strategy & Success Metrics",
            description: "Go-to-market strategy and key performance indicators",
            sectionType: "launch",
            order: 6,
            questions: [
                PRDQuestion(
                    text: "What is your app launch timeline?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 1,
                    placeholder: "Describe development phases, beta testing, and launch dates",
                    helpText: "Include milestones for MVP, beta release, and public launch"
                ),
                PRDQuestion(
                    text: "What marketing and user acquisition strategies will you use?",
                    questionType: .multipleChoice,
                    options: ["App Store Optimization", "Social Media Marketing", "Content Marketing", "Paid Advertising", "Influencer Marketing", "PR/Media Outreach", "Referral Programs", "Partnership Marketing"],
                    isRequired: true,
                    order: 2,
                    placeholder: nil,
                    helpText: "Select marketing channels for user acquisition"
                ),
                PRDQuestion(
                    text: "What are your key success metrics and KPIs?",
                    questionType: .multipleChoice,
                    options: ["Downloads/Installs", "Daily Active Users", "Monthly Active Users", "User Retention Rate", "Session Duration", "Revenue per User", "Conversion Rate", "App Store Rating"],
                    isRequired: true,
                    order: 3,
                    placeholder: nil,
                    helpText: "Select metrics to measure app success"
                ),
                PRDQuestion(
                    text: "What are your goals for the first 6 months after launch?",
                    questionType: .richText,
                    options: nil,
                    isRequired: true,
                    order: 4,
                    placeholder: "Set specific, measurable goals for user acquisition, revenue, etc.",
                    helpText: "Include quantitative targets for downloads, users, revenue, or other key metrics"
                )
            ],
            isRequired: true
        )
    ]
}