import SwiftUI

struct TemplateBuilderView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var templateService = TemplateService.shared
    
    @State private var templateName = ""
    @State private var templateDescription = ""
    @State private var templateIcon = "doc.text"
    @State private var sections: [CustomSection] = []
    @State private var showingAddSection = false
    @State private var editingSection: CustomSection?
    
    struct CustomSection: Identifiable {
        let id = UUID()
        var title: String
        var description: String
        var questions: [CustomQuestion]
        var isRequired: Bool
        
        init(title: String = "", description: String = "", questions: [CustomQuestion] = [], isRequired: Bool = true) {
            self.title = title
            self.description = description
            self.questions = questions
            self.isRequired = isRequired
        }
    }
    
    struct CustomQuestion: Identifiable {
        let id = UUID()
        var text: String
        var questionType: QuestionType
        var options: [String]
        var isRequired: Bool
        var placeholder: String
        var helpText: String
        
        init(text: String = "", questionType: QuestionType = .textInput, options: [String] = [], isRequired: Bool = false, placeholder: String = "", helpText: String = "") {
            self.text = text
            self.questionType = questionType
            self.options = options
            self.isRequired = isRequired
            self.placeholder = placeholder
            self.helpText = helpText
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                VStack(spacing: 16) {
                    HStack {
                        Image(systemName: templateIcon)
                            .font(.system(size: 40))
                            .foregroundColor(.accentColor)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Custom Template Builder")
                                .font(.title2)
                                .fontWeight(.bold)
                            
                            Text("Create a custom PRD template for your specific needs")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                    }
                    
                    // Template Basic Info
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Template Name")
                                    .font(.headline)
                                
                                TextField("Enter template name", text: $templateName)
                                    .textFieldStyle(.roundedBorder)
                            }
                            
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Icon")
                                    .font(.headline)
                                
                                IconPickerView(selectedIcon: $templateIcon)
                            }
                        }
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Description")
                                .font(.headline)
                            
                            TextField("Describe what this template is for", text: $templateDescription)
                                .textFieldStyle(.roundedBorder)
                        }
                    }
                    .padding()
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(8)
                }
                .padding()
                
                Divider()
                
                // Sections List
                VStack(alignment: .leading, spacing: 0) {
                    HStack {
                        Text("Template Sections")
                            .font(.headline)
                        
                        Spacer()
                        
                        Button("Add Section") {
                            showingAddSection = true
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.small)
                    }
                    .padding()
                    
                    if sections.isEmpty {
                        EmptyTemplateView {
                            showingAddSection = true
                        }
                    } else {
                        List {
                            ForEach(Array(sections.enumerated()), id: \.element.id) { index, section in
                                TemplateSectionRowView(
                                    section: section,
                                    index: index
                                ) {
                                    editingSection = section
                                } onDelete: {
                                    sections.remove(at: index)
                                } onMoveUp: {
                                    if index > 0 {
                                        sections.swapAt(index, index - 1)
                                    }
                                } onMoveDown: {
                                    if index < sections.count - 1 {
                                        sections.swapAt(index, index + 1)
                                    }
                                }
                            }
                        }
                        .listStyle(PlainListStyle())
                    }
                }
                
                Spacer()
                
                // Action Buttons
                HStack {
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    
                    Spacer()
                    
                    Button("Preview Template") {
                        // Show preview
                    }
                    .buttonStyle(.bordered)
                    .disabled(sections.isEmpty || templateName.isEmpty)
                    
                    Button("Save Template") {
                        saveTemplate()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(sections.isEmpty || templateName.isEmpty)
                }
                .padding()
            }
            .navigationTitle("Template Builder")
            .navigationBarTitleDisplayMode(.inline)
        }
        .frame(width: 800, height: 700)
        .sheet(isPresented: $showingAddSection) {
            SectionBuilderView { newSection in
                sections.append(newSection)
            }
        }
        .sheet(item: $editingSection) { section in
            if let index = sections.firstIndex(where: { $0.id == section.id }) {
                SectionBuilderView(section: section) { updatedSection in
                    sections[index] = updatedSection
                }
            }
        }
    }
    
    private func saveTemplate() {
        // Convert custom sections to PRDSection format
        let prdSections = sections.enumerated().map { index, section in
            let prdQuestions = section.questions.enumerated().map { qIndex, question in
                PRDQuestion(
                    text: question.text,
                    questionType: question.questionType,
                    options: question.options.isEmpty ? nil : question.options,
                    isRequired: question.isRequired,
                    order: qIndex,
                    placeholder: question.placeholder.isEmpty ? nil : question.placeholder,
                    helpText: question.helpText.isEmpty ? nil : question.helpText
                )
            }
            
            return PRDSection(
                title: section.title,
                description: section.description,
                sectionType: "custom",
                order: index,
                questions: prdQuestions,
                isRequired: section.isRequired
            )
        }
        
        // Save template (this would be implemented in TemplateService)
        // templateService.saveCustomTemplate(name: templateName, description: templateDescription, sections: prdSections)
        
        dismiss()
    }
}

struct IconPickerView: View {
    @Binding var selectedIcon: String
    @State private var showingIconPicker = false
    
    let availableIcons = [
        "doc.text", "graduationcap", "person.2", "book", "app", "globe",
        "lightbulb", "gear", "chart.bar", "target", "flag", "star",
        "heart", "briefcase", "building", "car", "house", "phone"
    ]
    
    var body: some View {
        Button(action: { showingIconPicker = true }) {
            HStack {
                Image(systemName: selectedIcon)
                    .foregroundColor(.accentColor)
                Text("Choose")
                    .font(.caption)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color(NSColor.controlBackgroundColor))
            .cornerRadius(6)
        }
        .buttonStyle(.plain)
        .popover(isPresented: $showingIconPicker) {
            VStack {
                Text("Choose Icon")
                    .font(.headline)
                    .padding()
                
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 6), spacing: 8) {
                    ForEach(availableIcons, id: \.self) { icon in
                        Button(action: {
                            selectedIcon = icon
                            showingIconPicker = false
                        }) {
                            Image(systemName: icon)
                                .font(.title2)
                                .foregroundColor(selectedIcon == icon ? .white : .accentColor)
                                .frame(width: 40, height: 40)
                                .background(selectedIcon == icon ? Color.accentColor : Color.clear)
                                .cornerRadius(8)
                        }
                        .buttonStyle(.plain)
                    }
                }
                .padding()
            }
            .frame(width: 300, height: 200)
        }
    }
}

struct TemplateSectionRowView: View {
    let section: TemplateBuilderView.CustomSection
    let index: Int
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onMoveUp: () -> Void
    let onMoveDown: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(index + 1). \(section.title)")
                        .font(.headline)
                    
                    if !section.description.isEmpty {
                        Text(section.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(2)
                    }
                    
                    Text("\(section.questions.count) questions")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack {
                    HStack {
                        Button(action: onMoveUp) {
                            Image(systemName: "chevron.up")
                        }
                        .buttonStyle(.borderless)
                        .disabled(index == 0)
                        
                        Button(action: onMoveDown) {
                            Image(systemName: "chevron.down")
                        }
                        .buttonStyle(.borderless)
                    }
                    
                    HStack {
                        Button("Edit") {
                            onEdit()
                        }
                        .buttonStyle(.borderless)
                        
                        Button("Delete") {
                            onDelete()
                        }
                        .buttonStyle(.borderless)
                        .foregroundColor(.red)
                    }
                }
            }
            
            if section.isRequired {
                HStack {
                    Image(systemName: "asterisk")
                        .font(.caption2)
                        .foregroundColor(.red)
                    Text("Required Section")
                        .font(.caption2)
                        .foregroundColor(.red)
                }
            }
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
}

struct EmptyTemplateView: View {
    let onAddSection: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "plus.circle")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Sections Yet")
                .font(.title3)
                .fontWeight(.medium)
            
            Text("Add sections to define the structure of your custom template")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 40)
            
            Button("Add First Section") {
                onAddSection()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding()
    }
}

struct SectionBuilderView: View {
    @Environment(\.dismiss) private var dismiss
    
    let onSave: (TemplateBuilderView.CustomSection) -> Void
    
    @State private var sectionTitle: String
    @State private var sectionDescription: String
    @State private var isRequired: Bool
    @State private var questions: [TemplateBuilderView.CustomQuestion]
    @State private var showingAddQuestion = false
    
    init(section: TemplateBuilderView.CustomSection? = nil, onSave: @escaping (TemplateBuilderView.CustomSection) -> Void) {
        self.onSave = onSave
        self._sectionTitle = State(initialValue: section?.title ?? "")
        self._sectionDescription = State(initialValue: section?.description ?? "")
        self._isRequired = State(initialValue: section?.isRequired ?? true)
        self._questions = State(initialValue: section?.questions ?? [])
    }
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                // Section Info
                VStack(alignment: .leading, spacing: 12) {
                    Text("Section Information")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Title")
                            .font(.subheadline)
                        TextField("Section title", text: $sectionTitle)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Description")
                            .font(.subheadline)
                        TextField("Brief description of this section", text: $sectionDescription)
                            .textFieldStyle(.roundedBorder)
                    }
                    
                    Toggle("Required Section", isOn: $isRequired)
                }
                .padding()
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(8)
                
                // Questions
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Questions")
                            .font(.headline)
                        
                        Spacer()
                        
                        Button("Add Question") {
                            showingAddQuestion = true
                        }
                        .buttonStyle(.borderedProminent)
                        .controlSize(.small)
                    }
                    
                    if questions.isEmpty {
                        Text("No questions added yet")
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity, minHeight: 100)
                            .background(Color(NSColor.controlBackgroundColor))
                            .cornerRadius(8)
                    } else {
                        ForEach(Array(questions.enumerated()), id: \.element.id) { index, question in
                            QuestionRowView(question: question) {
                                questions.remove(at: index)
                            }
                        }
                    }
                }
                
                Spacer()
                
                // Actions
                HStack {
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    
                    Spacer()
                    
                    Button("Save Section") {
                        let section = TemplateBuilderView.CustomSection(
                            title: sectionTitle,
                            description: sectionDescription,
                            questions: questions,
                            isRequired: isRequired
                        )
                        onSave(section)
                        dismiss()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(sectionTitle.isEmpty)
                }
            }
            .padding()
            .navigationTitle("Section Builder")
            .navigationBarTitleDisplayMode(.inline)
        }
        .frame(width: 600, height: 500)
        .sheet(isPresented: $showingAddQuestion) {
            QuestionBuilderView { newQuestion in
                questions.append(newQuestion)
            }
        }
    }
}

struct QuestionRowView: View {
    let question: TemplateBuilderView.CustomQuestion
    let onDelete: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(question.text)
                    .font(.subheadline)
                    .lineLimit(2)
                
                HStack {
                    Text(question.questionType.rawValue.capitalized)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.accentColor.opacity(0.1))
                        .cornerRadius(4)
                    
                    if question.isRequired {
                        Text("Required")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.red.opacity(0.1))
                            .foregroundColor(.red)
                            .cornerRadius(4)
                    }
                }
            }
            
            Spacer()
            
            Button("Delete") {
                onDelete()
            }
            .buttonStyle(.borderless)
            .foregroundColor(.red)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(6)
    }
}

struct QuestionBuilderView: View {
    @Environment(\.dismiss) private var dismiss
    
    let onSave: (TemplateBuilderView.CustomQuestion) -> Void
    
    @State private var questionText = ""
    @State private var questionType: QuestionType = .textInput
    @State private var isRequired = false
    @State private var placeholder = ""
    @State private var helpText = ""
    @State private var options: [String] = [""]
    
    var body: some View {
        NavigationView {
            Form {
                Section("Question Details") {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Question Text")
                        TextEditor(text: $questionText)
                            .frame(minHeight: 60)
                    }
                    
                    Picker("Question Type", selection: $questionType) {
                        ForEach(QuestionType.allCases, id: \.self) { type in
                            Text(type.rawValue.capitalized).tag(type)
                        }
                    }
                    
                    Toggle("Required Question", isOn: $isRequired)
                }
                
                if questionType == .multipleChoice || questionType == .singleSelect {
                    Section("Options") {
                        ForEach(options.indices, id: \.self) { index in
                            HStack {
                                TextField("Option \(index + 1)", text: $options[index])
                                
                                if options.count > 1 {
                                    Button("Remove") {
                                        options.remove(at: index)
                                    }
                                    .foregroundColor(.red)
                                }
                            }
                        }
                        
                        Button("Add Option") {
                            options.append("")
                        }
                    }
                }
                
                Section("Additional Settings") {
                    TextField("Placeholder text", text: $placeholder)
                    TextField("Help text", text: $helpText)
                }
            }
            .navigationTitle("Question Builder")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") { dismiss() }
                }
                
                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        let question = TemplateBuilderView.CustomQuestion(
                            text: questionText,
                            questionType: questionType,
                            options: questionType == .multipleChoice || questionType == .singleSelect ? options.filter { !$0.isEmpty } : [],
                            isRequired: isRequired,
                            placeholder: placeholder,
                            helpText: helpText
                        )
                        onSave(question)
                        dismiss()
                    }
                    .disabled(questionText.isEmpty)
                }
            }
        }
        .frame(width: 500, height: 400)
    }
}

#Preview {
    TemplateBuilderView()
}