import SwiftUI
import CoreData

struct DocumentLibraryView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var dataService = DataService.shared
    
    @State private var searchText = ""
    @State private var selectedProjectType: ProjectType?
    @State private var sortOption: SortOption = .modifiedDate
    @State private var showingCompletedOnly = false
    @State private var showingNewDocumentSheet = false
    @State private var selectedDocument: PRDDocument?
    
    enum SortOption: String, CaseIterable {
        case title = "Title"
        case modifiedDate = "Last Modified"
        case createdDate = "Created Date"
        case projectType = "Project Type"
        case completion = "Completion"
        
        var keyPath: KeyPath<PRDDocument, some Comparable> {
            switch self {
            case .title:
                return \PRDDocument.title
            case .modifiedDate:
                return \PRDDocument.modifiedDate
            case .createdDate:
                return \PRDDocument.createdDate
            case .projectType:
                return \PRDDocument.projectType
            case .completion:
                return \PRDDocument.completionPercentage
            }
        }
    }
    
    var filteredDocuments: [PRDDocument] {
        let documents = dataService.fetchDocuments()
        
        return documents.filter { document in
            // Search filter
            if !searchText.isEmpty {
                let titleMatch = document.title?.localizedCaseInsensitiveContains(searchText) ?? false
                let typeMatch = document.projectType?.localizedCaseInsensitiveContains(searchText) ?? false
                if !titleMatch && !typeMatch {
                    return false
                }
            }
            
            // Project type filter
            if let selectedType = selectedProjectType {
                if document.projectType != selectedType.rawValue {
                    return false
                }
            }
            
            // Completion filter
            if showingCompletedOnly && !document.isFullyCompleted {
                return false
            }
            
            return true
        }.sorted { doc1, doc2 in
            switch sortOption {
            case .title:
                return (doc1.title ?? "") < (doc2.title ?? "")
            case .modifiedDate:
                return (doc1.modifiedDate ?? Date.distantPast) > (doc2.modifiedDate ?? Date.distantPast)
            case .createdDate:
                return (doc1.createdDate ?? Date.distantPast) > (doc2.createdDate ?? Date.distantPast)
            case .projectType:
                return (doc1.projectType ?? "") < (doc2.projectType ?? "")
            case .completion:
                return doc1.completionPercentage > doc2.completionPercentage
            }
        }
    }
    
    var body: some View {
        NavigationSplitView {
            VStack(spacing: 0) {
                // Search and Filter Header
                VStack(spacing: 12) {
                    // Search Bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        
                        TextField("Search documents...", text: $searchText)
                            .textFieldStyle(.plain)
                        
                        if !searchText.isEmpty {
                            Button(action: { searchText = "" }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                            .buttonStyle(.plain)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(8)
                    
                    // Filters
                    HStack {
                        // Project Type Filter
                        Menu {
                            Button("All Types") {
                                selectedProjectType = nil
                            }
                            
                            Divider()
                            
                            ForEach(ProjectType.allCases) { type in
                                Button(action: {
                                    selectedProjectType = type
                                }) {
                                    HStack {
                                        Image(systemName: type.icon)
                                        Text(type.rawValue)
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Image(systemName: "line.3.horizontal.decrease.circle")
                                Text(selectedProjectType?.rawValue ?? "All Types")
                                Image(systemName: "chevron.down")
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(NSColor.controlBackgroundColor))
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)
                        
                        // Sort Options
                        Menu {
                            ForEach(SortOption.allCases, id: \.self) { option in
                                Button(action: {
                                    sortOption = option
                                }) {
                                    HStack {
                                        Text(option.rawValue)
                                        if sortOption == option {
                                            Image(systemName: "checkmark")
                                        }
                                    }
                                }
                            }
                        } label: {
                            HStack {
                                Image(systemName: "arrow.up.arrow.down")
                                Text(sortOption.rawValue)
                                Image(systemName: "chevron.down")
                            }
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color(NSColor.controlBackgroundColor))
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)
                        
                        Spacer()
                        
                        // Completed Only Toggle
                        Toggle("Completed", isOn: $showingCompletedOnly)
                            .toggleStyle(.checkbox)
                            .font(.caption)
                    }
                }
                .padding()
                .background(Color(NSColor.windowBackgroundColor))
                
                Divider()
                
                // Document List
                if filteredDocuments.isEmpty {
                    EmptyLibraryView(
                        hasDocuments: !dataService.fetchDocuments().isEmpty,
                        searchText: searchText
                    ) {
                        showingNewDocumentSheet = true
                    }
                } else {
                    List(filteredDocuments, id: \.id, selection: $selectedDocument) { document in
                        EnhancedDocumentRowView(document: document)
                            .tag(document)
                            .contextMenu {
                                DocumentContextMenu(document: document, dataService: dataService)
                            }
                    }
                    .listStyle(SidebarListStyle())
                }
            }
            .navigationTitle("Document Library")
            .toolbar {
                ToolbarItemGroup(placement: .primaryAction) {
                    Button(action: { showingNewDocumentSheet = true }) {
                        Label("New Document", systemImage: "plus")
                    }
                    .keyboardShortcut("n", modifiers: .command)
                }
            }
        } detail: {
            if let selectedDocument = selectedDocument {
                DocumentDetailView(document: selectedDocument)
            } else if filteredDocuments.isEmpty && !dataService.fetchDocuments().isEmpty {
                SearchEmptyStateView(searchText: searchText)
            } else {
                DocumentSelectionPromptView()
            }
        }
        .frame(minWidth: 900, minHeight: 600)
        .sheet(isPresented: $showingNewDocumentSheet) {
            NewDocumentView(dataService: dataService)
        }
        .onAppear {
            dataService.startAutoSave()
        }
        .onDisappear {
            dataService.stopAutoSave()
        }
    }
}

struct EnhancedDocumentRowView: View {
    @ObservedObject var document: PRDDocument
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                // Project type icon
                Image(systemName: ProjectType(rawValue: document.projectType ?? "General")?.icon ?? "doc.text")
                    .foregroundColor(.accentColor)
                    .font(.title3)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(document.title ?? "Untitled Document")
                        .font(.headline)
                        .lineLimit(2)
                    
                    Text(document.projectType ?? "General")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    if document.isFullyCompleted {
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Complete")
                                .font(.caption2)
                                .foregroundColor(.green)
                        }
                    } else {
                        Text("\(Int(document.completionPercentage * 100))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(document.modifiedDate ?? Date(), style: .relative)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
            
            // Progress bar
            ProgressView(value: document.completionPercentage)
                .progressViewStyle(LinearProgressViewStyle(tint: document.isFullyCompleted ? .green : .accentColor))
                .scaleEffect(y: 0.8)
            
            // Section summary
            HStack {
                Text("\(document.sectionsArray.count) sections")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                let totalQuestions = document.sectionsArray.reduce(0) { $0 + $1.questionsArray.count }
                let answeredQuestions = document.sectionsArray.reduce(0) { total, section in
                    total + section.questionsArray.filter { question in
                        !(question.answer?.value?.isEmpty ?? true)
                    }.count
                }
                
                Text("\(answeredQuestions)/\(totalQuestions) questions")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

struct DocumentContextMenu: View {
    let document: PRDDocument
    let dataService: DataService
    
    @State private var showingDeleteAlert = false
    
    var body: some View {
        Group {
            Button("Duplicate") {
                let _ = dataService.duplicateDocument(document)
            }
            
            Button("Export...") {
                // This would trigger export sheet
            }
            
            Divider()
            
            Button("Delete", role: .destructive) {
                showingDeleteAlert = true
            }
        }
        .alert("Delete Document", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                dataService.deleteDocument(document)
            }
        } message: {
            Text("Are you sure you want to delete \"\(document.title ?? "this document")\"? This action cannot be undone.")
        }
    }
}

struct EmptyLibraryView: View {
    let hasDocuments: Bool
    let searchText: String
    let onCreateDocument: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: hasDocuments ? "magnifyingglass" : "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            if hasDocuments {
                Text("No documents found")
                    .font(.title2)
                    .fontWeight(.medium)
                
                if !searchText.isEmpty {
                    Text("No documents match \"\(searchText)\"")
                        .font(.body)
                        .foregroundColor(.secondary)
                } else {
                    Text("Try adjusting your filters")
                        .font(.body)
                        .foregroundColor(.secondary)
                }
            } else {
                Text("Welcome to PRD Generator")
                    .font(.title2)
                    .fontWeight(.medium)
                
                Text("Create your first Product Requirements Document to get started")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 40)
                
                Button("Create Your First PRD") {
                    onCreateDocument()
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct SearchEmptyStateView: View {
    let searchText: String
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("No Results")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("No documents match \"\(searchText)\"")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    DocumentLibraryView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}