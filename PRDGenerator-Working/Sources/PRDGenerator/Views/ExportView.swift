import SwiftUI
import UniformTypeIdentifiers

struct ExportView: View {
    let document: PRDDocument
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedFormat: ExportFormat = .markdown
    @State private var includeEmptyAnswers = false
    @State private var includeMetadata = true
    @State private var showingFilePicker = false
    @State private var exportURL: URL?
    
    enum ExportFormat: String, CaseIterable {
        case markdown = "Markdown"
        case pdf = "PDF"
        case html = "HTML"
        case plainText = "Plain Text"
        
        var fileExtension: String {
            switch self {
            case .markdown: return "md"
            case .pdf: return "pdf"
            case .html: return "html"
            case .plainText: return "txt"
            }
        }
        
        var icon: String {
            switch self {
            case .markdown: return "doc.text"
            case .pdf: return "doc.richtext"
            case .html: return "globe"
            case .plainText: return "doc.plaintext"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.system(size: 48))
                        .foregroundColor(.accentColor)
                    
                    Text("Export Document")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Export \"\(document.title ?? "Untitled")\" to share or archive")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Export Format Selection
                VStack(alignment: .leading, spacing: 12) {
                    Text("Export Format")
                        .font(.headline)
                    
                    LazyVGrid(columns: [
                        GridItem(.flexible()),
                        GridItem(.flexible())
                    ], spacing: 12) {
                        ForEach(ExportFormat.allCases, id: \.self) { format in
                            ExportFormatCard(
                                format: format,
                                isSelected: selectedFormat == format
                            ) {
                                selectedFormat = format
                            }
                        }
                    }
                }
                
                // Export Options
                VStack(alignment: .leading, spacing: 12) {
                    Text("Export Options")
                        .font(.headline)
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Toggle("Include empty answers", isOn: $includeEmptyAnswers)
                        Toggle("Include document metadata", isOn: $includeMetadata)
                    }
                    .padding()
                    .background(Color(NSColor.controlBackgroundColor))
                    .cornerRadius(8)
                }
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 12) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                    
                    Button("Export") {
                        exportDocument()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                }
            }
            .padding()
            .navigationTitle("Export")
            .navigationBarTitleDisplayMode(.inline)
        }
        .frame(width: 500, height: 600)
        .fileExporter(
            isPresented: $showingFilePicker,
            document: ExportableDocument(
                content: generateExportContent(),
                format: selectedFormat
            ),
            contentType: contentType,
            defaultFilename: defaultFilename
        ) { result in
            switch result {
            case .success(let url):
                print("Document exported to: \(url)")
                dismiss()
            case .failure(let error):
                print("Export failed: \(error)")
            }
        }
    }
    
    private var contentType: UTType {
        switch selectedFormat {
        case .markdown:
            return UTType.text
        case .pdf:
            return UTType.pdf
        case .html:
            return UTType.html
        case .plainText:
            return UTType.plainText
        }
    }
    
    private var defaultFilename: String {
        let title = document.title?.replacingOccurrences(of: " ", with: "_") ?? "PRD_Document"
        return "\(title).\(selectedFormat.fileExtension)"
    }
    
    private func exportDocument() {
        showingFilePicker = true
    }
    
    private func generateExportContent() -> String {
        // Use the new PRDGenerationService for better formatting
        let exportFormat: ExportFormat
        switch selectedFormat {
        case .markdown:
            exportFormat = .markdown
        case .html:
            exportFormat = .html
        case .pdf, .plainText:
            exportFormat = .plainText
        }
        
        return PRDGenerationService.shared.generatePRDContent(for: document, format: exportFormat)
    }
    
    private func generateMetadata() -> String {
        var metadata = ""
        
        switch selectedFormat {
        case .markdown:
            metadata += "## Document Information\n\n"
            metadata += "- **Project Type:** \(document.projectType ?? "General")\n"
            metadata += "- **Created:** \(formatDate(document.createdDate))\n"
            metadata += "- **Last Modified:** \(formatDate(document.modifiedDate))\n"
            metadata += "- **Completion:** \(Int(document.completionPercentage * 100))%\n"
        case .pdf, .rtf, .plainText:
            metadata += "Document Information\n"
            metadata += "-------------------\n"
            metadata += "Project Type: \(document.projectType ?? "General")\n"
            metadata += "Created: \(formatDate(document.createdDate))\n"
            metadata += "Last Modified: \(formatDate(document.modifiedDate))\n"
            metadata += "Completion: \(Int(document.completionPercentage * 100))%\n"
        }
        
        return metadata
    }
    
    private func generateSectionContent(_ section: Section) -> String {
        var content = ""
        
        // Section title
        switch selectedFormat {
        case .markdown:
            content += "## \(section.title ?? "Untitled Section")\n\n"
        case .pdf, .rtf, .plainText:
            content += "\(section.title ?? "Untitled Section")\n"
            content += String(repeating: "-", count: (section.title ?? "Untitled Section").count)
            content += "\n\n"
        }
        
        // Questions and answers
        for question in section.questionsArray {
            content += generateQuestionContent(question)
            content += "\n"
        }
        
        return content
    }
    
    private func generateQuestionContent(_ question: Question) -> String {
        var content = ""
        
        // Question text
        switch selectedFormat {
        case .markdown:
            content += "### \(question.text ?? "")\n\n"
        case .pdf, .rtf, .plainText:
            content += "\(question.text ?? "")\n"
        }
        
        // Answer
        if let answer = question.answer?.value, !answer.isEmpty {
            content += "\(answer)\n"
        } else if includeEmptyAnswers {
            content += "_[No answer provided]_\n"
        }
        
        return content
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "Unknown" }
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

struct ExportFormatCard: View {
    let format: ExportView.ExportFormat
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: format.icon)
                    .font(.system(size: 24))
                    .foregroundColor(isSelected ? .white : .accentColor)
                
                Text(format.rawValue)
                    .font(.headline)
                    .foregroundColor(isSelected ? .white : .primary)
            }
            .frame(maxWidth: .infinity, minHeight: 80)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.accentColor : Color(NSColor.controlBackgroundColor))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.accentColor : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
        }
        .buttonStyle(.plain)
    }
}

struct ExportableDocument: FileDocument {
    static var readableContentTypes: [UTType] { [.plainText, .rtf] }
    
    let content: String
    let format: ExportView.ExportFormat
    
    init(content: String, format: ExportView.ExportFormat) {
        self.content = content
        self.format = format
    }
    
    init(configuration: ReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents,
              let string = String(data: data, encoding: .utf8)
        else {
            throw CocoaError(.fileReadCorruptFile)
        }
        self.content = string
        self.format = .plainText
    }
    
    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = content.data(using: .utf8)!
        return .init(regularFileWithContents: data)
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleDocument = PRDDocument(context: context)
    sampleDocument.title = "Sample L&D Platform"
    sampleDocument.projectType = "Learning & Development"
    
    return ExportView(document: sampleDocument)
}