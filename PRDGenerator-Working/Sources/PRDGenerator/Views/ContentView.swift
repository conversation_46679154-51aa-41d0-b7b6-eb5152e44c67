import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @StateObject private var dataService = DataService.shared
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \PRDDocument.modifiedDate, ascending: false)],
        animation: .default)
    private var documents: FetchedResults<PRDDocument>
    
    @State private var showingNewDocumentSheet = false
    @State private var selectedDocument: PRDDocument?

    var body: some View {
        NavigationSplitView {
            // Sidebar - Document List
            List {
                Section("Recent Documents") {
                    ForEach(documents) { document in
                        DocumentRowView(document: document)
                            .onTapGesture {
                                selectedDocument = document
                            }
                    }
                    .onDelete(perform: deleteDocuments)
                }
            }
            .navigationTitle("PRD Generator")
            .toolbar {
                ToolbarItem(placement: .primaryAction) {
                    Button(action: { showingNewDocumentSheet = true }) {
                        Label("Add Document", systemImage: "plus")
                    }
                }
            }
        } detail: {
            // Detail View
            if let selectedDocument = selectedDocument {
                DocumentDetailView(document: selectedDocument)
            } else if documents.isEmpty {
                WelcomeView(showingNewDocumentSheet: $showingNewDocumentSheet)
            } else {
                DocumentSelectionPromptView()
            }
        }
        .frame(minWidth: 800, minHeight: 600)
        .sheet(isPresented: $showingNewDocumentSheet) {
            NewDocumentView(dataService: dataService)
        }
        .onAppear {
            dataService.startAutoSave()
        }
        .onDisappear {
            dataService.stopAutoSave()
        }
    }


    private func deleteDocuments(offsets: IndexSet) {
        withAnimation {
            offsets.map { documents[$0] }.forEach(viewContext.delete)

            do {
                try viewContext.save()
            } catch {
                let nsError = error as NSError
                fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

struct DocumentRowView: View {
    @ObservedObject var document: PRDDocument
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(document.title ?? "Untitled")
                    .font(.headline)
                    .lineLimit(1)
                
                HStack {
                    Image(systemName: ProjectType(rawValue: document.projectType ?? "General")?.icon ?? "doc.text")
                        .foregroundColor(.accentColor)
                        .font(.caption)
                    
                    Text(document.projectType ?? "General")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(document.modifiedDate ?? Date(), style: .relative)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                if document.isFullyCompleted {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Text("\(Int(document.completionPercentage * 100))%")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                Circle()
                    .fill(document.completionPercentage >= 1.0 ? Color.green : Color.accentColor)
                    .frame(width: 8, height: 8)
            }
        }
        .padding(.vertical, 4)
    }
}

struct WelcomeView: View {
    @Binding var showingNewDocumentSheet: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text")
                .font(.system(size: 60))
                .foregroundColor(.accentColor)
            
            Text("Welcome to PRD Generator")
                .font(.largeTitle)
                .fontWeight(.bold)
            
            Text("Create professional Product Requirements Documents for your L&D projects, coaching platforms, and app development.")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 40)
            
            Button("Create Your First PRD") {
                showingNewDocumentSheet = true
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}

struct DocumentSelectionPromptView: View {
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 60))
                .foregroundColor(.secondary)
            
            Text("Select a Document")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("Choose a document from the sidebar to view and edit its contents.")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)
                .padding(.horizontal, 40)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#Preview("Welcome View") {
    WelcomeView(showingNewDocumentSheet: .constant(false))
}

#Preview("Document Selection Prompt") {
    DocumentSelectionPromptView()
}