import SwiftUI
import CoreData

struct SectionDetailView: View {
    @ObservedObject var section: Section
    let dataService: DataService
    
    @State private var showingAddQuestion = false
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                // Section Header
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(section.title ?? "Untitled Section")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                            
                            Text("\(section.questionsArray.count) questions • \(Int(section.completionPercentage * 100))% complete")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        if section.isCompleted {
                            HStack {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                                Text("Complete")
                                    .font(.caption)
                                    .foregroundColor(.green)
                            }
                        }
                    }
                    
                    ProgressView(value: section.completionPercentage)
                        .progressViewStyle(LinearProgressViewStyle())
                }
                .padding()
                .background(Color(NSColor.controlBackgroundColor))
                .cornerRadius(12)
                
                // Questions
                if section.questionsArray.isEmpty {
                    EmptyQuestionsView {
                        showingAddQuestion = true
                    }
                } else {
                    LazyVStack(spacing: 16) {
                        ForEach(section.questionsArray, id: \.id) { question in
                            QuestionView(question: question, dataService: dataService)
                        }
                    }
                }
                
                Spacer(minLength: 50)
            }
            .padding()
        }
        .navigationTitle(section.title ?? "Untitled Section")
        .toolbar {
            ToolbarItem(placement: .primaryAction) {
                Button(action: { showingAddQuestion = true }) {
                    Label("Add Question", systemImage: "plus")
                }
            }
        }
        .sheet(isPresented: $showingAddQuestion) {
            AddQuestionView(section: section, dataService: dataService)
        }
    }
}

struct QuestionView: View {
    @ObservedObject var question: Question
    let dataService: DataService
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Question Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(question.text ?? "")
                            .font(.headline)
                            .fontWeight(.medium)
                        
                        if question.isRequired {
                            Text("*")
                                .foregroundColor(.red)
                                .font(.headline)
                        }
                    }
                    
                    Text(question.questionTypeEnum.rawValue.capitalized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.accentColor.opacity(0.1))
                        .cornerRadius(4)
                }
                
                Spacer()
                
                if question.isAnswered {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                }
            }
            
            // Question Input
            QuestionInputView(question: question, dataService: dataService)
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }
}

struct QuestionInputView: View {
    @ObservedObject var question: Question
    let dataService: DataService
    
    @State private var textValue: String = ""
    @State private var selectedOptions: Set<String> = []
    @State private var selectedOption: String = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            switch question.questionTypeEnum {
            case .textInput:
                TextField("Enter your answer...", text: $textValue)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        dataService.updateAnswer(for: question, value: textValue)
                    }
                
            case .richText:
                VStack(alignment: .leading) {
                    Text("Answer:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    TextEditor(text: $textValue)
                        .frame(minHeight: 100)
                        .padding(4)
                        .background(Color(NSColor.textBackgroundColor))
                        .cornerRadius(4)
                        .overlay(
                            RoundedRectangle(cornerRadius: 4)
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                }
                
            case .multipleChoice:
                VStack(alignment: .leading, spacing: 8) {
                    Text("Select all that apply:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ForEach(question.optionsArray, id: \.self) { option in
                        HStack {
                            Button(action: {
                                if selectedOptions.contains(option) {
                                    selectedOptions.remove(option)
                                } else {
                                    selectedOptions.insert(option)
                                }
                                updateMultipleChoiceAnswer()
                            }) {
                                HStack {
                                    Image(systemName: selectedOptions.contains(option) ? "checkmark.square.fill" : "square")
                                        .foregroundColor(selectedOptions.contains(option) ? .accentColor : .secondary)
                                    Text(option)
                                        .foregroundColor(.primary)
                                    Spacer()
                                }
                            }
                            .buttonStyle(.plain)
                        }
                        .padding(.vertical, 2)
                    }
                }
                
            case .singleSelect:
                VStack(alignment: .leading, spacing: 8) {
                    Text("Select one:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    ForEach(question.optionsArray, id: \.self) { option in
                        HStack {
                            Button(action: {
                                selectedOption = option
                                dataService.updateAnswer(for: question, value: option)
                            }) {
                                HStack {
                                    Image(systemName: selectedOption == option ? "largecircle.fill.circle" : "circle")
                                        .foregroundColor(selectedOption == option ? .accentColor : .secondary)
                                    Text(option)
                                        .foregroundColor(.primary)
                                    Spacer()
                                }
                            }
                            .buttonStyle(.plain)
                        }
                        .padding(.vertical, 2)
                    }
                }
                
            case .dateInput:
                DatePicker("Select date", selection: Binding(
                    get: {
                        if let dateString = question.answer?.value,
                           let date = ISO8601DateFormatter().date(from: dateString) {
                            return date
                        }
                        return Date()
                    },
                    set: { date in
                        let dateString = ISO8601DateFormatter().string(from: date)
                        dataService.updateAnswer(for: question, value: dateString)
                    }
                ), displayedComponents: .date)
                .datePickerStyle(.compact)
                
            case .numberInput:
                TextField("Enter number...", text: $textValue)
                    .textFieldStyle(.roundedBorder)
                    .keyboardType(.numberPad)
                    .onSubmit {
                        dataService.updateAnswer(for: question, value: textValue)
                    }
                
            case .urlInput:
                TextField("Enter URL...", text: $textValue)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        dataService.updateAnswer(for: question, value: textValue)
                    }
                
            case .emailInput:
                TextField("Enter email...", text: $textValue)
                    .textFieldStyle(.roundedBorder)
                    .keyboardType(.emailAddress)
                    .onSubmit {
                        dataService.updateAnswer(for: question, value: textValue)
                    }
            }
        }
        .onAppear {
            loadCurrentAnswer()
        }
        .onChange(of: textValue) { newValue in
            if question.questionTypeEnum == .richText {
                dataService.updateAnswer(for: question, value: newValue)
            }
        }
    }
    
    private func loadCurrentAnswer() {
        if let currentValue = question.answer?.value {
            switch question.questionTypeEnum {
            case .multipleChoice:
                selectedOptions = Set(currentValue.components(separatedBy: ", "))
            case .singleSelect:
                selectedOption = currentValue
            default:
                textValue = currentValue
            }
        }
    }
    
    private func updateMultipleChoiceAnswer() {
        let answer = Array(selectedOptions).joined(separator: ", ")
        dataService.updateAnswer(for: question, value: answer)
    }
}

struct EmptyQuestionsView: View {
    let onAddQuestion: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "questionmark.circle")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            
            Text("No Questions Yet")
                .font(.title2)
                .fontWeight(.medium)
            
            Text("This section doesn't have any questions. Add questions to start building your PRD.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Add First Question") {
                onAddQuestion()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, minHeight: 200)
        .padding()
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let sampleSection = Section(context: context)
    sampleSection.title = "Sample Section"
    
    return SectionDetailView(section: sampleSection, dataService: DataService.shared)
        .environment(\.managedObjectContext, context)
}