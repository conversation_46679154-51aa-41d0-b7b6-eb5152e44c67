import SwiftUI

struct NewDocumentView: View {
    @Environment(\.dismiss) private var dismiss
    let dataService: DataService
    
    @State private var documentTitle = ""
    @State private var selectedProjectType: ProjectType = .general
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "doc.text.fill")
                        .font(.system(size: 48))
                        .foregroundColor(.accentColor)
                    
                    Text("Create New PRD")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Choose a template to get started with your Product Requirements Document")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                // Form
                VStack(alignment: .leading, spacing: 16) {
                    // Document Title
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Document Title")
                            .font(.headline)
                        
                        TextField("Enter document title", text: $documentTitle)
                            .textFieldStyle(.roundedBorder)
                            .onSubmit {
                                if !documentTitle.isEmpty {
                                    createDocument()
                                }
                            }
                    }
                    
                    // Project Type Selection
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Project Type")
                            .font(.headline)
                        
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 12) {
                            ForEach(ProjectType.allCases) { projectType in
                                ProjectTypeCard(
                                    projectType: projectType,
                                    isSelected: selectedProjectType == projectType
                                ) {
                                    selectedProjectType = projectType
                                }
                            }
                        }
                    }
                }
                .padding(.horizontal)
                
                Spacer()
                
                // Action Buttons
                HStack(spacing: 12) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.large)
                    
                    Button("Create Document") {
                        createDocument()
                    }
                    .buttonStyle(.borderedProminent)
                    .controlSize(.large)
                    .disabled(documentTitle.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
                .padding(.horizontal)
            }
            .padding()
            .navigationTitle("New Document")
            .navigationBarTitleDisplayMode(.inline)
        }
        .frame(width: 600, height: 700)
    }
    
    private func createDocument() {
        let trimmedTitle = documentTitle.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTitle.isEmpty else { return }
        
        let _ = dataService.createDocument(title: trimmedTitle, projectType: selectedProjectType)
        dismiss()
    }
}

struct ProjectTypeCard: View {
    let projectType: ProjectType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: projectType.icon)
                    .font(.system(size: 32))
                    .foregroundColor(isSelected ? .white : .accentColor)
                
                VStack(spacing: 4) {
                    Text(projectType.rawValue)
                        .font(.headline)
                        .foregroundColor(isSelected ? .white : .primary)
                    
                    Text(projectType.description)
                        .font(.caption)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : .secondary)
                        .multilineTextAlignment(.center)
                        .lineLimit(3)
                }
            }
            .frame(maxWidth: .infinity, minHeight: 120)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.accentColor : Color(NSColor.controlBackgroundColor))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.accentColor : Color.gray.opacity(0.3), lineWidth: isSelected ? 2 : 1)
            )
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    NewDocumentView(dataService: DataService.shared)
}