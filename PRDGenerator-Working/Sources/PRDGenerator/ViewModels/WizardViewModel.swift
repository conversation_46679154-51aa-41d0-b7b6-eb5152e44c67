import Foundation
import CoreData
import Swift<PERSON>

@MainActor
class WizardViewModel: ObservableObject {
    @Published var currentSectionIndex = 0
    @Published var currentQuestionIndex = 0
    @Published var isCompleted = false
    @Published var answers: [UUID: String] = [:]
    
    private let document: PRDDocument
    private let dataService: DataService
    
    var sections: [Section] {
        document.sectionsArray
    }
    
    var currentSection: Section? {
        guard currentSectionIndex < sections.count else { return nil }
        return sections[currentSectionIndex]
    }
    
    var currentQuestion: Question? {
        guard let section = currentSection else { return nil }
        let questions = section.questionsArray
        guard currentQuestionIndex < questions.count else { return nil }
        return questions[currentQuestionIndex]
    }
    
    var totalQuestions: Int {
        sections.reduce(0) { $0 + $1.questionsArray.count }
    }
    
    var currentQuestionNumber: Int {
        var count = 0
        for (sectionIndex, section) in sections.enumerated() {
            if sectionIndex < currentSectionIndex {
                count += section.questionsArray.count
            } else if sectionIndex == currentSectionIndex {
                count += currentQuestionIndex + 1
                break
            }
        }
        return count
    }
    
    var progress: Double {
        guard totalQuestions > 0 else { return 0 }
        return Double(currentQuestionNumber - 1) / Double(totalQuestions)
    }
    
    var canGoNext: Bool {
        guard let question = currentQuestion else { return false }
        
        if question.isRequired {
            return isCurrentQuestionAnswered
        }
        return true
    }
    
    var canGoPrevious: Bool {
        return currentSectionIndex > 0 || currentQuestionIndex > 0
    }
    
    var isCurrentQuestionAnswered: Bool {
        guard let question = currentQuestion else { return false }
        return answers[question.id!] != nil && !(answers[question.id!]?.isEmpty ?? true)
    }
    
    init(document: PRDDocument, dataService: DataService) {
        self.document = document
        self.dataService = dataService
        loadExistingAnswers()
    }
    
    func loadExistingAnswers() {
        answers.removeAll()
        
        for section in sections {
            for question in section.questionsArray {
                if let answer = question.answer?.value, !answer.isEmpty {
                    answers[question.id!] = answer
                }
            }
        }
    }
    
    func updateAnswer(for questionId: UUID, value: String) {
        answers[questionId] = value
        
        // Save to Core Data
        if let question = findQuestion(by: questionId) {
            dataService.updateAnswer(for: question, value: value)
        }
    }
    
    func getAnswer(for questionId: UUID) -> String {
        return answers[questionId] ?? ""
    }
    
    func goToNext() {
        guard canGoNext else { return }
        
        let currentSectionQuestions = currentSection?.questionsArray.count ?? 0
        
        if currentQuestionIndex < currentSectionQuestions - 1 {
            // Move to next question in current section
            currentQuestionIndex += 1
        } else if currentSectionIndex < sections.count - 1 {
            // Move to first question of next section
            currentSectionIndex += 1
            currentQuestionIndex = 0
        } else {
            // Completed all questions
            isCompleted = true
        }
    }
    
    func goToPrevious() {
        guard canGoPrevious else { return }
        
        if currentQuestionIndex > 0 {
            // Move to previous question in current section
            currentQuestionIndex -= 1
        } else if currentSectionIndex > 0 {
            // Move to last question of previous section
            currentSectionIndex -= 1
            currentQuestionIndex = max(0, (sections[currentSectionIndex].questionsArray.count) - 1)
        }
    }
    
    func goToQuestion(sectionIndex: Int, questionIndex: Int) {
        guard sectionIndex < sections.count else { return }
        guard questionIndex < sections[sectionIndex].questionsArray.count else { return }
        
        currentSectionIndex = sectionIndex
        currentQuestionIndex = questionIndex
    }
    
    func skipQuestion() {
        // Clear answer for current question if it exists
        if let questionId = currentQuestion?.id {
            answers.removeValue(forKey: questionId)
            if let question = currentQuestion {
                dataService.updateAnswer(for: question, value: "")
            }
        }
        
        goToNext()
    }
    
    func completeWizard() {
        // Mark document as completed if all required questions are answered
        let allRequiredAnswered = sections.allSatisfy { section in
            section.questionsArray.allSatisfy { question in
                if question.isRequired {
                    return answers[question.id!] != nil && !(answers[question.id!]?.isEmpty ?? true)
                }
                return true
            }
        }
        
        document.isCompleted = allRequiredAnswered
        dataService.updateDocument(document)
        isCompleted = true
    }
    
    private func findQuestion(by id: UUID) -> Question? {
        for section in sections {
            for question in section.questionsArray {
                if question.id == id {
                    return question
                }
            }
        }
        return nil
    }
    
    func getSectionProgress(for sectionIndex: Int) -> Double {
        guard sectionIndex < sections.count else { return 0 }
        let section = sections[sectionIndex]
        let questions = section.questionsArray
        
        guard !questions.isEmpty else { return 0 }
        
        let answeredCount = questions.filter { question in
            answers[question.id!] != nil && !(answers[question.id!]?.isEmpty ?? true)
        }.count
        
        return Double(answeredCount) / Double(questions.count)
    }
}