import CoreData
import Foundation

struct PersistenceController {
    static let shared = PersistenceController()

    static var preview: PersistenceController = {
        let result = PersistenceController(inMemory: true)
        let viewContext = result.container.viewContext
        
        // Create sample data for previews
        let sampleDocument = PRDDocument(context: viewContext)
        sampleDocument.id = UUID()
        sampleDocument.title = "Sample L&D Platform PRD"
        sampleDocument.projectType = "Learning & Development"
        sampleDocument.createdDate = Date().addingTimeInterval(-86400) // 1 day ago
        sampleDocument.modifiedDate = Date()
        sampleDocument.isCompleted = false
        
        let sampleSection = Section(context: viewContext)
        sampleSection.id = UUID()
        sampleSection.title = "Project Overview"
        sampleSection.content = "This is a sample section for the L&D platform PRD."
        sampleSection.order = 1
        sampleSection.sectionType = "overview"
        sampleSection.document = sampleDocument
        
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            fatalError("Unresolved error \(nsError), \(nsError.userInfo)")
        }
        return result
    }()

    let container: NSPersistentContainer

    init(inMemory: Bool = false) {
        container = NSPersistentContainer(name: "PRDGenerator")
        if inMemory {
            container.persistentStoreDescriptions.first!.url = URL(fileURLWithPath: "/dev/null")
        }
        
        container.loadPersistentStores(completionHandler: { (storeDescription, error) in
            if let error = error as NSError? {
                fatalError("Unresolved error \(error), \(error.userInfo)")
            }
        })
        
        container.viewContext.automaticallyMergesChangesFromParent = true
    }
}