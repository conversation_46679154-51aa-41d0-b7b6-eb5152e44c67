import Foundation
import CoreData

// MARK: - PRDDocument Extensions
extension PRDDocument {
    
    // MARK: - Computed Properties
    var sectionsArray: [Section] {
        let set = sections as? Set<Section> ?? []
        return set.sorted { $0.order < $1.order }
    }
    
    var completionPercentage: Double {
        let totalSections = sectionsArray.count
        guard totalSections > 0 else { return 0.0 }
        
        let completedSections = sectionsArray.filter { section in
            guard let questions = section.questionsArray as [Question]? else { return false }
            return questions.allSatisfy { $0.answer?.value?.isEmpty == false }
        }.count
        
        return Double(completedSections) / Double(totalSections)
    }
    
    var isFullyCompleted: Bool {
        return completionPercentage >= 1.0
    }
    
    // MARK: - Helper Methods
    func addSection(title: String, sectionType: String, order: Int16) -> Section {
        let section = Section(context: managedObjectContext!)
        section.id = UUID()
        section.title = title
        section.sectionType = sectionType
        section.order = order
        section.document = self
        return section
    }
    
    func updateModifiedDate() {
        modifiedDate = Date()
    }
    
    // MARK: - Template Application
    func applyTemplate(projectType: ProjectType) {
        // Clear existing sections
        sectionsArray.forEach { managedObjectContext?.delete($0) }
        
        // Add sections from template
        for (index, templateSection) in projectType.sections.enumerated() {
            let section = addSection(
                title: templateSection.title,
                sectionType: templateSection.sectionType,
                order: Int16(index)
            )
            
            // Add questions to section
            for (questionIndex, templateQuestion) in templateSection.questions.enumerated() {
                let question = section.addQuestion(
                    text: templateQuestion.text,
                    questionType: templateQuestion.questionType.rawValue,
                    isRequired: templateQuestion.isRequired,
                    order: Int16(questionIndex)
                )
                
                if let options = templateQuestion.options {
                    question.options = options.joined(separator: "|")
                }
            }
        }
        
        self.projectType = projectType.rawValue
        updateModifiedDate()
    }
}