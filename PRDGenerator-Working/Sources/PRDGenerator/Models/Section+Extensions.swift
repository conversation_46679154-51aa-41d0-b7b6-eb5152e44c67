import Foundation
import CoreData

// MARK: - Section Extensions
extension Section {
    
    // MARK: - Computed Properties
    var questionsArray: [Question] {
        let set = questions as? Set<Question> ?? []
        return set.sorted { $0.order < $1.order }
    }
    
    var isCompleted: Bool {
        return questionsArray.allSatisfy { question in
            guard let answer = question.answer else { return !question.isRequired }
            return !answer.value?.isEmpty ?? !question.isRequired
        }
    }
    
    var completionPercentage: Double {
        let totalQuestions = questionsArray.count
        guard totalQuestions > 0 else { return 0.0 }
        
        let answeredQuestions = questionsArray.filter { question in
            guard let answer = question.answer else { return false }
            return !(answer.value?.isEmpty ?? true)
        }.count
        
        return Double(answeredQuestions) / Double(totalQuestions)
    }
    
    // MARK: - Helper Methods
    func addQuestion(text: String, questionType: String, isRequired: Bool, order: Int16) -> Question {
        let question = Question(context: managedObjectContext!)
        question.id = UUID()
        question.text = text
        question.questionType = questionType
        question.isRequired = isRequired
        question.order = order
        question.section = self
        
        // Create an empty answer
        let answer = Answer(context: managedObjectContext!)
        answer.id = UUID()
        answer.value = ""
        answer.question = question
        
        return question
    }
    
    func updateDocument() {
        document?.updateModifiedDate()
    }
}