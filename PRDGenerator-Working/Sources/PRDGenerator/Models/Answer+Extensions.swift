import Foundation
import CoreData

// MARK: - Answer Extensions
extension Answer {
    
    // MARK: - Computed Properties
    var isEmpty: Bool {
        return value?.isEmpty ?? true
    }
    
    var hasContent: Bool {
        return !isEmpty
    }
    
    // MARK: - Helper Methods
    func updateValue(_ newValue: String) {
        value = newValue
        question?.section?.updateDocument()
    }
    
    func clear() {
        value = ""
        question?.section?.updateDocument()
    }
}