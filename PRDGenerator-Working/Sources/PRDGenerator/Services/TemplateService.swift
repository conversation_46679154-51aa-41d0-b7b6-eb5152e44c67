import Foundation

class TemplateService {
    static let shared = TemplateService()
    
    private init() {}
    
    // MARK: - Template Management
    
    func getTemplate(for projectType: ProjectType) -> [PRDSection] {
        return projectType.sections
    }
    
    func getAllProjectTypes() -> [ProjectType] {
        return ProjectType.allCases
    }
    
    func getProjectType(from string: String) -> ProjectType {
        return ProjectType(rawValue: string) ?? .general
    }
    
    // MARK: - Template Validation
    
    func validateTemplate(_ sections: [PRDSection]) -> [String] {
        var errors: [String] = []
        
        for section in sections {
            if section.title.isEmpty {
                errors.append("Section title cannot be empty")
            }
            
            for question in section.questions {
                if question.text.isEmpty {
                    errors.append("Question text cannot be empty in section '\(section.title)'")
                }
                
                if question.questionType == .multipleChoice || question.questionType == .singleSelect {
                    if question.options?.isEmpty ?? true {
                        errors.append("Multiple choice/select questions must have options in section '\(section.title)'")
                    }
                }
            }
        }
        
        return errors
    }
    
    // MARK: - Custom Template Creation
    
    func createCustomSection(title: String, description: String, questions: [PRDQuestion]) -> PRDSection {
        let maxOrder = questions.map { $0.order }.max() ?? 0
        
        return PRDSection(
            title: title,
            description: description,
            sectionType: "custom",
            order: maxOrder + 1,
            questions: questions,
            isRequired: false
        )
    }
    
    func createCustomQuestion(
        text: String,
        questionType: QuestionType,
        options: [String]? = nil,
        isRequired: Bool = false,
        placeholder: String? = nil,
        helpText: String? = nil
    ) -> PRDQuestion {
        return PRDQuestion(
            text: text,
            questionType: questionType,
            options: options,
            isRequired: isRequired,
            order: 1,
            placeholder: placeholder,
            helpText: helpText
        )
    }
    
    // MARK: - Template Export/Import
    
    func exportTemplate(_ sections: [PRDSection]) -> Data? {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            return try encoder.encode(sections)
        } catch {
            print("Failed to export template: \(error)")
            return nil
        }
    }
    
    func importTemplate(from data: Data) -> [PRDSection]? {
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode([PRDSection].self, from: data)
        } catch {
            print("Failed to import template: \(error)")
            return nil
        }
    }
}

// MARK: - Template Codable Support
extension PRDSection: Codable {}
extension PRDQuestion: Codable {}
extension QuestionType: Codable {}