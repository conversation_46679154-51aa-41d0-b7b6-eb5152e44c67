import Foundation
import PDFKit
import AppKit

class PDFExportService {
    static let shared = PDFExportService()
    
    private init() {}
    
    // MARK: - PDF Generation
    
    func generatePDF(for document: PRDDocument, includeMetadata: Bool = true) -> PDFDocument? {
        let pdfDocument = PDFDocument()
        
        // Generate content using PRDGenerationService
        let content = PRDGenerationService.shared.generatePRDContent(for: document, format: .html)
        
        // Create styled HTML content
        let styledHTML = createStyledHTML(content: content, document: document)
        
        // Convert HTML to PDF
        guard let pdfData = createPDFFromHTML(styledHTML) else {
            return nil
        }
        
        guard let pdf = PDFDocument(data: pdfData) else {
            return nil
        }
        
        // Add metadata
        if includeMetadata {
            addMetadata(to: pdf, document: document)
        }
        
        // Add page numbers and headers
        addPageNumbersAndHeaders(to: pdf, document: document)
        
        return pdf
    }
    
    // MARK: - HTML Styling
    
    private func createStyledHTML(content: String, document: PRDDocument) -> String {
        let css = generateCSS()
        
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>\(document.title ?? "PRD Document")</title>
            <style>
            \(css)
            </style>
        </head>
        <body>
            <div class="document">
                \(content)
            </div>
        </body>
        </html>
        """
    }
    
    private func generateCSS() -> String {
        return """
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px 20px;
            background: white;
        }
        
        .document {
            background: white;
        }
        
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        h2 {
            color: #34495e;
            border-bottom: 2px solid #ecf0f1;
            padding-bottom: 8px;
            margin-top: 40px;
            margin-bottom: 20px;
            font-size: 1.8em;
            font-weight: 400;
        }
        
        h3 {
            color: #2c3e50;
            margin-top: 30px;
            margin-bottom: 15px;
            font-size: 1.3em;
            font-weight: 500;
        }
        
        .document-meta {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .document-meta p {
            margin: 5px 0;
            font-size: 0.95em;
        }
        
        p {
            margin-bottom: 15px;
            text-align: justify;
        }
        
        ul, ol {
            margin-bottom: 20px;
            padding-left: 30px;
        }
        
        li {
            margin-bottom: 8px;
        }
        
        blockquote {
            border-left: 4px solid #3498db;
            margin: 20px 0;
            padding: 10px 20px;
            background: #f8f9fa;
            font-style: italic;
        }
        
        code {
            background: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 0.9em;
        }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            overflow-x: auto;
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 0.95em;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        
        tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        a {
            color: #3498db;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-break {
            page-break-inside: avoid;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 20px;
            }
            
            .document {
                box-shadow: none;
            }
            
            h1, h2 {
                page-break-after: avoid;
            }
            
            h3, h4, h5, h6 {
                page-break-after: avoid;
            }
        }
        
        .toc {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .toc h2 {
            margin-top: 0;
            border-bottom: none;
            color: #2c3e50;
        }
        
        .toc ol {
            padding-left: 20px;
        }
        
        .toc a {
            color: #2c3e50;
            text-decoration: none;
        }
        
        .toc a:hover {
            color: #3498db;
            text-decoration: underline;
        }
        
        .executive-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin: 30px 0;
        }
        
        .executive-summary h2 {
            color: white;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            margin-top: 0;
        }
        
        .executive-summary h3 {
            color: rgba(255,255,255,0.9);
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .status-complete {
            background: #27ae60;
            color: white;
        }
        
        .status-progress {
            background: #f39c12;
            color: white;
        }
        
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .highlight h4 {
            margin-top: 0;
            color: #856404;
        }
        """
    }
    
    // MARK: - PDF Creation
    
    private func createPDFFromHTML(_ html: String) -> Data? {
        guard let htmlData = html.data(using: .utf8) else { return nil }
        
        let webView = WKWebView()
        webView.frame = CGRect(x: 0, y: 0, width: 595, height: 842) // A4 size in points
        
        let semaphore = DispatchSemaphore(value: 0)
        var pdfData: Data?
        
        webView.loadHTMLString(html, baseURL: nil)
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            let printFormatter = webView.viewPrintFormatter()
            printFormatter.perPageContentInsets = NSEdgeInsets(top: 72, left: 72, bottom: 72, right: 72)
            
            let printInfo = NSPrintInfo()
            printInfo.paperSize = CGSize(width: 595, height: 842) // A4
            printInfo.topMargin = 72
            printInfo.bottomMargin = 72
            printInfo.leftMargin = 72
            printInfo.rightMargin = 72
            
            let printOperation = NSPrintOperation(view: NSView(), printInfo: printInfo)
            printOperation.printPanel = nil
            printOperation.showsPrintPanel = false
            
            // Create PDF data
            let pdfInfo = NSMutableDictionary()
            pdfInfo[kCGPDFContextCreator as String] = "PRD Generator"
            pdfInfo[kCGPDFContextAuthor as String] = "PRD Generator App"
            
            let dataConsumer = CGDataConsumer(data: NSMutableData())!
            let context = CGContext(consumer: dataConsumer, mediaBox: nil, pdfInfo)!
            
            context.beginPDFPage(nil)
            
            // Render the web view content
            if let layer = webView.layer {
                layer.render(in: context)
            }
            
            context.endPDFPage()
            context.closePDF()
            
            if let data = dataConsumer.data {
                pdfData = data as Data
            }
            
            semaphore.signal()
        }
        
        semaphore.wait()
        return pdfData
    }
    
    // MARK: - Metadata and Headers
    
    private func addMetadata(to pdf: PDFDocument, document: PRDDocument) {
        let attributes: [PDFDocumentAttribute: Any] = [
            .titleAttribute: document.title ?? "PRD Document",
            .authorAttribute: "PRD Generator",
            .subjectAttribute: "Product Requirements Document - \(document.projectType ?? "General")",
            .creatorAttribute: "PRD Generator App",
            .producerAttribute: "PRD Generator PDF Service",
            .creationDateAttribute: document.createdDate ?? Date(),
            .modificationDateAttribute: document.modifiedDate ?? Date(),
            .keywordsAttribute: [document.projectType ?? "PRD", "Requirements", "Product"]
        ]
        
        pdf.documentAttributes = attributes
    }
    
    private func addPageNumbersAndHeaders(to pdf: PDFDocument, document: PRDDocument) {
        let pageCount = pdf.pageCount
        
        for i in 0..<pageCount {
            guard let page = pdf.page(at: i) else { continue }
            
            // Add header
            let headerText = document.title ?? "PRD Document"
            addHeader(to: page, text: headerText)
            
            // Add page number
            let pageNumberText = "Page \(i + 1) of \(pageCount)"
            addPageNumber(to: page, text: pageNumberText)
        }
    }
    
    private func addHeader(to page: PDFPage, text: String) {
        let bounds = page.bounds(for: .mediaBox)
        let headerRect = CGRect(x: 72, y: bounds.height - 50, width: bounds.width - 144, height: 20)
        
        let headerAnnotation = PDFAnnotation(bounds: headerRect, forType: .freeText, withProperties: nil)
        headerAnnotation.contents = text
        headerAnnotation.font = NSFont.systemFont(ofSize: 10, weight: .medium)
        headerAnnotation.fontColor = NSColor.gray
        headerAnnotation.backgroundColor = NSColor.clear
        headerAnnotation.border = nil
        
        page.addAnnotation(headerAnnotation)
    }
    
    private func addPageNumber(to page: PDFPage, text: String) {
        let bounds = page.bounds(for: .mediaBox)
        let footerRect = CGRect(x: bounds.width - 144, y: 36, width: 72, height: 20)
        
        let footerAnnotation = PDFAnnotation(bounds: footerRect, forType: .freeText, withProperties: nil)
        footerAnnotation.contents = text
        footerAnnotation.font = NSFont.systemFont(ofSize: 9)
        footerAnnotation.fontColor = NSColor.gray
        footerAnnotation.backgroundColor = NSColor.clear
        footerAnnotation.border = nil
        footerAnnotation.alignment = .right
        
        page.addAnnotation(footerAnnotation)
    }
    
    // MARK: - Save PDF
    
    func savePDF(_ pdf: PDFDocument, to url: URL) -> Bool {
        return pdf.write(to: url)
    }
}

import WebKit