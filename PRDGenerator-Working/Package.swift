// swift-tools-version: 5.9
import PackageDescription

let package = Package(
    name: "PRDGenerator",
    platforms: [
        .macOS(.v12)
    ],
    products: [
        .executable(name: "PRDGenerator", targets: ["PRDGenerator"])
    ],
    dependencies: [],
    targets: [
        .executableTarget(
            name: "PRDGenerator",
            dependencies: [],
            path: "Sources/PRDGenerator",
            resources: [
                .process("Resources"),
                .process("Models/PRDGenerator.xcdatamodeld")
            ]
        )
    ]
)